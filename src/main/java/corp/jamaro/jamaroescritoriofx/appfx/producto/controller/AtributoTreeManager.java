package corp.jamaro.jamaroescritoriofx.appfx.producto.controller;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.*;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoFiltro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.AtributoGroupingService;
import javafx.beans.property.ReadOnlyObjectWrapper;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.scene.control.*;
import javafx.scene.control.cell.TextFieldTreeTableCell;
import javafx.util.Callback;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kordamp.ikonli.javafx.FontIcon;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Consumer;

/**
 * Manager para manejar el TreeTableView de atributos.
 * Gestiona la creación, actualización y edición de la estructura de árbol.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AtributoTreeManager {
    
    private final AtributoGroupingService groupingService;
    
    private TreeTableView<AtributoTreeItem> treeTableView;
    private TreeTableColumn<AtributoTreeItem, String> colNombre;
    private TreeTableColumn<AtributoTreeItem, String> colValor;
    private TreeTableColumn<AtributoTreeItem, Void> colAcciones;
    
    private ObservableList<Atributo> atributosData;
    private Consumer<Atributo> onEditAtributo;
    
    /**
     * Inicializa el TreeTableView con las columnas y configuraciones necesarias
     */
    public void initialize(TreeTableView<AtributoTreeItem> treeTableView,
                          TreeTableColumn<AtributoTreeItem, String> colNombre,
                          TreeTableColumn<AtributoTreeItem, String> colValor,
                          TreeTableColumn<AtributoTreeItem, Void> colAcciones,
                          ObservableList<Atributo> atributosData) {
        
        this.treeTableView = treeTableView;
        this.colNombre = colNombre;
        this.colValor = colValor;
        this.colAcciones = colAcciones;
        this.atributosData = atributosData;
        
        setupColumns();
        setupTreeTableView();
    }
    
    /**
     * Configura las columnas del TreeTableView
     */
    private void setupColumns() {
        // Configurar constraints responsivos para las columnas
        setupColumnConstraints();

        // Columna Nombre/Filtro
        colNombre.setCellValueFactory(cellData -> {
            AtributoTreeItem item = cellData.getValue().getValue();
            return new ReadOnlyObjectWrapper<>(item.getDisplayText());
        });
        colNombre.setSortable(false); // Deshabilitar ordenamiento
        colNombre.setResizable(true);

        // Columna Valor - editable para atributos
        colValor.setCellValueFactory(cellData -> {
            AtributoTreeItem item = cellData.getValue().getValue();
            return new ReadOnlyObjectWrapper<>(item.getDisplayValue());
        });

        colValor.setCellFactory(createValueCellFactory());
        colValor.setOnEditCommit(this::handleValueEdit);
        colValor.setSortable(false); // Deshabilitar ordenamiento
        colValor.setResizable(true);

        // Columna Acciones/Tipo
        colAcciones.setCellValueFactory(cellData -> {
            AtributoTreeItem item = cellData.getValue().getValue();
            return new ReadOnlyObjectWrapper<>(null); // Usamos cell factory para el contenido
        });
        colAcciones.setCellFactory(createActionsCellFactory());
        colAcciones.setSortable(false); // Deshabilitar ordenamiento
        colAcciones.setResizable(true);
    }

    /**
     * Configura constraints responsivos para las columnas
     */
    private void setupColumnConstraints() {
        // Configurar anchos mínimos y máximos para responsividad
        colNombre.setMinWidth(150);
        colNombre.setPrefWidth(200);
        colNombre.setMaxWidth(Double.MAX_VALUE);

        colValor.setMinWidth(120);
        colValor.setPrefWidth(180);
        colValor.setMaxWidth(Double.MAX_VALUE);

        colAcciones.setMinWidth(80);
        colAcciones.setPrefWidth(120);
        colAcciones.setMaxWidth(200);
    }
    
    /**
     * Configura el TreeTableView
     */
    private void setupTreeTableView() {
        treeTableView.setEditable(true);
        treeTableView.setShowRoot(false);
        treeTableView.setRowFactory(this::createRowFactory);

        // Configurar política de redimensionamiento de columnas más estable
        treeTableView.setColumnResizePolicy(TreeTableView.CONSTRAINED_RESIZE_POLICY);

        // Configuraciones adicionales para mejor renderizado
        treeTableView.setTableMenuButtonVisible(false);
        treeTableView.setFixedCellSize(-1); // Permitir altura variable de celdas

        // Crear root invisible
        TreeItem<AtributoTreeItem> root = new TreeItem<>();
        treeTableView.setRoot(root);

        // Agregar listener para manejar cambios de tamaño de ventana
        treeTableView.widthProperty().addListener((obs, oldWidth, newWidth) -> {
            // Forzar actualización del layout cuando cambia el ancho
            javafx.application.Platform.runLater(() -> {
                treeTableView.requestLayout();
            });
        });
    }
    
    /**
     * Actualiza el contenido del TreeTableView con los atributos agrupados
     */
    public void updateTreeContent(Set<Atributo> atributos, Set<Grupo> grupos) {
        // Asegurar que la actualización se ejecute en el hilo de JavaFX
        if (!javafx.application.Platform.isFxApplicationThread()) {
            javafx.application.Platform.runLater(() -> updateTreeContent(atributos, grupos));
            return;
        }

        TreeItem<AtributoTreeItem> root = treeTableView.getRoot();
        root.getChildren().clear();

        // Agrupar atributos
        Map<String, List<Atributo>> groupedAtributos =
                groupingService.groupExistingAtributos(atributos, grupos);

        // Crear nodos para cada grupo
        for (Map.Entry<String, List<Atributo>> entry : groupedAtributos.entrySet()) {
            String grupoNombre = entry.getKey();
            List<Atributo> atributosDelGrupo = entry.getValue();

            // Encontrar el grupo correspondiente (si no es "Extras")
            Grupo grupo = null;
            if (!"Extras".equals(grupoNombre)) {
                grupo = findGrupoByName(grupos, grupoNombre);
            }

            // Crear nodo grupo
            AtributoTreeItem grupoItem = new AtributoTreeItem(grupo, grupoNombre);
            TreeItem<AtributoTreeItem> grupoNode = new TreeItem<>(grupoItem);
            grupoNode.setExpanded(true);

            // Agregar listener para eventos de expand/collapse más estable
            grupoNode.expandedProperty().addListener((obs, wasExpanded, isExpanded) -> {
                // Usar un delay mínimo para evitar conflictos de renderizado
                javafx.application.Platform.runLater(() -> {
                    treeTableView.refresh();
                    treeTableView.requestLayout();
                });
            });

            // Agregar nodos atributo
            for (Atributo atributo : atributosDelGrupo) {
                AtributoTreeItem atributoItem = new AtributoTreeItem(atributo);
                TreeItem<AtributoTreeItem> atributoNode = new TreeItem<>(atributoItem);
                grupoNode.getChildren().add(atributoNode);
            }

            root.getChildren().add(grupoNode);
        }

        // Actualización final más suave sin forzar visibilidad de columnas
        treeTableView.refresh();
        treeTableView.requestLayout();

        log.debug("TreeTableView actualizado con {} grupos", groupedAtributos.size());
    }
    
    /**
     * Agrega atributos para un nuevo grupo
     */
    public void addAtributosForGrupo(Grupo grupo) {
        List<Atributo> nuevosAtributos = groupingService.createAtributosForGrupo(grupo);
        atributosData.addAll(nuevosAtributos);
        
        log.info("Agregados {} atributos para el grupo {}", 
                nuevosAtributos.size(), getGrupoPrincipalName(grupo));
    }
    
    /**
     * Crea la factory para las celdas de valor (editable según tipo de filtro)
     */
    private Callback<TreeTableColumn<AtributoTreeItem, String>, TreeTableCell<AtributoTreeItem, String>> 
            createValueCellFactory() {
        
        return column -> new TreeTableCell<AtributoTreeItem, String>() {
            private TextField textField;
            private Spinner<Double> numberSpinner;
            private ComboBox<String> comboBox;
            
            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);

                if (empty || getTreeTableRow() == null || getTreeTableRow().getItem() == null) {
                    setGraphic(null);
                    setText(null);
                    setEditable(false);
                    return;
                }

                AtributoTreeItem treeItem = getTreeTableRow().getItem();

                if (treeItem.isGrupo()) {
                    // Los grupos no son editables
                    setGraphic(null);
                    setText("");
                    setEditable(false);
                } else {
                    // Los atributos son editables según su tipo
                    setEditable(true);

                    if (isEditing()) {
                        Control editControl = createEditingControl(treeItem);
                        if (editControl != null) {
                            setGraphic(editControl);
                            setText(null);
                        } else {
                            // Fallback si no se puede crear el control
                            cancelEdit();
                        }
                    } else {
                        setGraphic(null);
                        setText(item != null ? item : "");
                    }
                }
            }
            
            @Override
            public void startEdit() {
                AtributoTreeItem treeItem = getTreeTableRow().getItem();
                if (treeItem != null && treeItem.isAtributo()) {
                    super.startEdit();
                    setGraphic(createEditingControl(treeItem));
                    setText(null);
                }
            }
            
            @Override
            public void cancelEdit() {
                super.cancelEdit();
                setGraphic(null);
                setText(getItem());
            }
            
            private Control createEditingControl(AtributoTreeItem treeItem) {
                if (treeItem == null || treeItem.getTipoFiltro() == null) {
                    return null;
                }

                TipoFiltro tipo = treeItem.getTipoFiltro();
                String currentValue = treeItem.getDisplayValue();
                if (currentValue == null) currentValue = "";

                try {
                    switch (tipo) {
                        case NUMERICO:
                            if (numberSpinner == null) {
                                numberSpinner = new Spinner<>(-Double.MAX_VALUE, Double.MAX_VALUE, 0.0, 0.1);
                                numberSpinner.setEditable(true);
                                numberSpinner.valueProperty().addListener((obs, oldVal, newVal) -> {
                                    if (newVal != null) {
                                        commitEdit(newVal.toString());
                                    }
                                });
                                // Configurar para que se ajuste al ancho de la celda
                                numberSpinner.setPrefWidth(Control.USE_COMPUTED_SIZE);
                                numberSpinner.setMaxWidth(Double.MAX_VALUE);
                            }
                            try {
                                double value = currentValue.isEmpty() ? 0.0 : Double.parseDouble(currentValue);
                                numberSpinner.getValueFactory().setValue(value);
                            } catch (NumberFormatException e) {
                                numberSpinner.getValueFactory().setValue(0.0);
                            }
                            return numberSpinner;

                        case DICOTOMICO:
                            if (comboBox == null) {
                                comboBox = new ComboBox<>(FXCollections.observableArrayList("", "Sí", "No"));
                                comboBox.setOnAction(e -> {
                                    String value = comboBox.getValue();
                                    commitEdit(value != null ? value : "");
                                });
                                // Configurar para que se ajuste al ancho de la celda
                                comboBox.setPrefWidth(Control.USE_COMPUTED_SIZE);
                                comboBox.setMaxWidth(Double.MAX_VALUE);
                            }
                            comboBox.setValue(currentValue);
                            return comboBox;

                        default: // CADENA_TEXTO, OPCION_MULTIPLE, COMPUESTO
                            if (textField == null) {
                                textField = new TextField();
                                textField.setOnAction(e -> commitEdit(textField.getText()));
                                textField.focusedProperty().addListener((obs, oldVal, newVal) -> {
                                    if (!newVal) commitEdit(textField.getText());
                                });
                                // Configurar para que se ajuste al ancho de la celda
                                textField.setPrefWidth(Control.USE_COMPUTED_SIZE);
                                textField.setMaxWidth(Double.MAX_VALUE);
                            }
                            textField.setText(currentValue);
                            return textField;
                    }
                } catch (Exception e) {
                    log.warn("Error creando control de edición para tipo {}: {}", tipo, e.getMessage());
                    return null;
                }
            }
        };
    }
    
    /**
     * Maneja la edición de valores
     */
    private void handleValueEdit(TreeTableColumn.CellEditEvent<AtributoTreeItem, String> event) {
        AtributoTreeItem item = event.getRowValue().getValue();
        if (item.isAtributo()) {
            String newValue = event.getNewValue();
            item.updateAtributoValue(newValue);
            
            // Actualizar en la lista observable
            int index = atributosData.indexOf(item.getAtributo());
            if (index >= 0) {
                atributosData.set(index, item.getAtributo());
            }
            
            log.debug("Valor actualizado para atributo {}: {}", 
                    item.getFiltroNombre(), newValue);
        }
    }
    
    /**
     * Crea la factory para las celdas de acciones - Muestra el tipo de filtro
     */
    private Callback<TreeTableColumn<AtributoTreeItem, Void>, TreeTableCell<AtributoTreeItem, Void>>
            createActionsCellFactory() {

        return column -> new TreeTableCell<AtributoTreeItem, Void>() {
            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);

                if (empty || getTreeTableRow() == null || getTreeTableRow().getItem() == null) {
                    setGraphic(null);
                    setText(null);
                    return;
                }

                AtributoTreeItem treeItem = getTreeTableRow().getItem();
                if (treeItem.isAtributo() && treeItem.getTipoFiltro() != null) {
                    // Mostrar el tipo de filtro de forma más legible
                    String tipoTexto = formatTipoFiltro(treeItem.getTipoFiltro());
                    setText(tipoTexto);
                    setGraphic(null);

                    // Aplicar estilo según el tipo
                    getStyleClass().removeAll("tipo-numerico", "tipo-texto", "tipo-dicotomico", "tipo-compuesto");
                    switch (treeItem.getTipoFiltro()) {
                        case NUMERICO:
                            getStyleClass().add("tipo-numerico");
                            break;
                        case DICOTOMICO:
                            getStyleClass().add("tipo-dicotomico");
                            break;
                        case COMPUESTO:
                            getStyleClass().add("tipo-compuesto");
                            break;
                        default:
                            getStyleClass().add("tipo-texto");
                            break;
                    }
                } else {
                    setGraphic(null);
                    setText("");
                    getStyleClass().removeAll("tipo-numerico", "tipo-texto", "tipo-dicotomico", "tipo-compuesto");
                }
            }
        };
    }

    /**
     * Formatea el tipo de filtro para mostrar de forma más legible.
     * Si el tipo es null, se considera como CADENA_TEXTO por defecto.
     */
    private String formatTipoFiltro(corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoFiltro tipo) {
        // Si el tipo es null, usar CADENA_TEXTO como valor por defecto
        if (tipo == null) {
            tipo = TipoFiltro.CADENA_TEXTO;
        }

        switch (tipo) {
            case CADENA_TEXTO:
                return "Texto";
            case NUMERICO:
                return "Número";
            case DICOTOMICO:
                return "Sí/No";
            case OPCION_MULTIPLE:
                return "Opciones";
            case COMPUESTO:
                return "Compuesto";
            default:
                return tipo.toString();
        }
    }

    /**
     * Fuerza un refresh suave del TreeTableView sin causar parpadeo
     */
    public void refreshTreeTableSmoothly() {
        if (treeTableView == null) return;

        // Usar un enfoque más suave para el refresh
        javafx.application.Platform.runLater(() -> {
            treeTableView.refresh();
            treeTableView.requestLayout();
        });
    }

    /**
     * Crea factory para las filas (para estilos diferentes)
     */
    private TreeTableRow<AtributoTreeItem> createRowFactory(TreeTableView<AtributoTreeItem> treeTableView) {
        return new TreeTableRow<AtributoTreeItem>() {
            @Override
            protected void updateItem(AtributoTreeItem item, boolean empty) {
                super.updateItem(item, empty);
                
                getStyleClass().removeAll("grupo-row", "atributo-row", "extra-row");
                
                if (!empty && item != null) {
                    if (item.isGrupo()) {
                        getStyleClass().add("grupo-row");
                        if ("Extras".equals(item.getGrupoNombre())) {
                            getStyleClass().add("extra-row");
                        }
                    } else {
                        getStyleClass().add("atributo-row");
                    }
                }
            }
        };
    }
    
    /**
     * Busca un grupo por su nombre principal
     */
    private Grupo findGrupoByName(Set<Grupo> grupos, String nombreBuscado) {
        return grupos.stream()
                .filter(grupo -> nombreBuscado.equals(getGrupoPrincipalName(grupo)))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * Obtiene el nombre principal de un grupo
     */
    private String getGrupoPrincipalName(Grupo grupo) {
        if (grupo.getNombresGrupo() != null && !grupo.getNombresGrupo().isEmpty()) {
            return grupo.getNombresGrupo().stream()
                    .filter(nombre -> Boolean.TRUE.equals(nombre.getIsPrincipal()))
                    .findFirst()
                    .map(NombreGrupo::getNombre)
                    .orElse(grupo.getNombresGrupo().iterator().next().getNombre());
        }
        return "Sin nombre";
    }
    
    // Setter para callback de edición
    public void setOnEditAtributo(Consumer<Atributo> onEditAtributo) {
        this.onEditAtributo = onEditAtributo;
    }
}
