package corp.jamaro.jamaroescritoriofx.appfx.caja.util;

import corp.jamaro.jamaroescritoriofx.appfx.caja.model.CobroDineroProgramado;
import corp.jamaro.jamaroescritoriofx.appfx.caja.model.gui.CajaGui;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.stage.StageStyle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.NumberFormat;
import java.util.List;
import java.util.Optional;

/**
 * Utilidad para crear y mostrar diálogos específicos relacionados con caja.
 * Centraliza la lógica de diálogos complejos para mantener los controladores más limpios.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CajaDialogUtil {

    private static final String STYLESHEET_PATH = "/css/styles.css";
    private static final String SEARCH_PRODUCT_STYLESHEET_PATH = "/css/searchProduct.css";

    private final AlertUtil alertUtil;
    private NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();

    /**
     * Muestra el diálogo de selección de caja con estilo moderno.
     */
    public Optional<CajaSelectionResult> showCajaSelectionDialog(List<CajaGui> cajaGuiList) {
        Dialog<CajaSelectionResult> dialog = new Dialog<>();
        dialog.setTitle("Seleccionar Caja");

        // Crear botones
        ButtonType selectButtonType = new ButtonType("Seleccionar", ButtonBar.ButtonData.OK_DONE);
        ButtonType addButtonType = new ButtonType("Agregar", ButtonBar.ButtonData.OTHER);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(selectButtonType, addButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));
        contentBox.setAlignment(Pos.CENTER);

        // Título
        Label titleLabel = new Label("Seleccione la caja con la que desea trabajar:");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 14px; -fx-font-weight: 600;");

        // ListView para las cajas
        ListView<CajaGui> listView = new ListView<>();
        listView.getItems().addAll(cajaGuiList);
        listView.setPrefHeight(200);
        listView.setMaxWidth(Double.MAX_VALUE);

        // Configurar cell factory para mejor visualización
        listView.setCellFactory(param -> new ListCell<CajaGui>() {
            @Override
            protected void updateItem(CajaGui item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                    setGraphic(null);
                } else {
                    setText(item.getNombreCaja());

                    // Crear un label secundario con el ID
                    Label idLabel = new Label("ID: " + item.getId().toString().substring(0, 8) + "...");
                    idLabel.setStyle("-fx-text-fill: #a0a0a0; -fx-font-size: 10px;");

                    VBox cellBox = new VBox(2);
                    cellBox.getChildren().addAll(
                        new Label(item.getNombreCaja()),
                        idLabel
                    );
                    setGraphic(cellBox);
                    setText(null);
                }
            }
        });

        contentBox.getChildren().addAll(titleLabel, listView);
        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-cash-register");

        // Enable/disable select button based on selection
        Button selectButton = (Button) dialog.getDialogPane().lookupButton(selectButtonType);
        selectButton.setDisable(true);
        listView.getSelectionModel().selectedItemProperty().addListener(
            (obs, oldSelection, newSelection) -> selectButton.setDisable(newSelection == null)
        );

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == selectButtonType) {
                CajaGui selectedCaja = listView.getSelectionModel().getSelectedItem();
                return selectedCaja != null ? new CajaSelectionResult(selectedCaja, false) : null;
            } else if (dialogButton == addButtonType) {
                return new CajaSelectionResult(null, true);
            }
            return null;
        });

        // Configurar focus en el ListView
        Platform.runLater(listView::requestFocus);

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo de procesamiento de pago con estilo moderno.
     */
    public Optional<PaymentData> showPaymentDialog(CobroDineroProgramado cobro) {
        Dialog<PaymentData> dialog = new Dialog<>();
        dialog.setTitle("Procesar Cobro");

        // Crear botones
        ButtonType processButtonType = new ButtonType("Procesar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(processButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));

        // Título
        Label titleLabel = new Label("Procesar Cobro - Restante: " + currencyFormat.format(cobro.getMontoRestante()));
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 14px; -fx-font-weight: 600;");

        // Información del cobro con mejor estilo
        VBox infoBox = new VBox(4);
        infoBox.setStyle("-fx-background-color: #2a2a2a; -fx-padding: 15; -fx-border-radius: 8; -fx-background-radius: 8;");

        Label idLabel = new Label("🆔 ID: " + cobro.getId().toString().substring(0, 8) + "...");
        idLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 12px; -fx-font-weight: bold;");

        Label montoLabel = new Label("💰 Monto restante: " + currencyFormat.format(cobro.getMontoRestante()));
        montoLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px;");

        Label iniciadoLabel = new Label("👤 Iniciado por: " + cobro.getIniciadoPor());
        iniciadoLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px;");

        infoBox.getChildren().addAll(idLabel, montoLabel, iniciadoLabel);

        // Campos de pago
        Label efectivoLabel = new Label("Monto en Efectivo:");
        efectivoLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextField efectivoField = new TextField();
        efectivoField.setPromptText("Ingrese monto en efectivo");
        efectivoField.setPrefWidth(200);

        Label digitalLabel = new Label("Monto Digital:");
        digitalLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextField digitalField = new TextField();
        digitalField.setPromptText("Ingrese monto digital");
        digitalField.setPrefWidth(200);

        Label detallesEfectivoLabel = new Label("Detalles Efectivo (opcional):");
        detallesEfectivoLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextField detallesEfectivoField = new TextField();
        detallesEfectivoField.setPromptText("Ej: Billetes, monedas, etc.");
        detallesEfectivoField.setPrefWidth(200);

        Label detallesDigitalLabel = new Label("Detalles Digital (opcional):");
        detallesDigitalLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextField detallesDigitalField = new TextField();
        detallesDigitalField.setPromptText("Ej: Transferencia, tarjeta, etc.");
        detallesDigitalField.setPrefWidth(200);

        // Total calculation
        Label totalLabel = new Label("Total a pagar: S/ 0.00");
        totalLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-weight: bold; -fx-font-size: 14px;");

        // Update total when amounts change
        Runnable updateTotal = () -> {
            try {
                String efectivoText = efectivoField.getText().trim();
                String digitalText = digitalField.getText().trim();

                double efectivo = efectivoText.isEmpty() ? 0.0 : Double.parseDouble(efectivoText);
                double digital = digitalText.isEmpty() ? 0.0 : Double.parseDouble(digitalText);
                double total = efectivo + digital;

                // Show total and change information
                String totalText = "Total a pagar: " + currencyFormat.format(total);
                if (total > cobro.getMontoRestante()) {
                    double change = total - cobro.getMontoRestante();
                    totalText += " (Vuelto: " + currencyFormat.format(change) + ")";
                    totalLabel.setStyle("-fx-text-fill: #4CAF50; -fx-font-weight: bold; -fx-font-size: 14px;");
                } else {
                    totalLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-weight: bold; -fx-font-size: 14px;");
                }
                totalLabel.setText(totalText);

                // Enable/disable process button based on valid total
                Button processButton = (Button) dialog.getDialogPane().lookupButton(processButtonType);
                // Enable button if total > 0 and at least one field has a value
                // Trust the server to handle overpayments and calculate change (vuelto)
                boolean hasValidInput = (!efectivoText.isEmpty() && efectivo > 0) || (!digitalText.isEmpty() && digital > 0);
                processButton.setDisable(!hasValidInput || total <= 0);
            } catch (NumberFormatException e) {
                totalLabel.setText("Total a pagar: Inválido");
                totalLabel.setStyle("-fx-text-fill: #ff6b6b; -fx-font-weight: bold; -fx-font-size: 14px;");
                Button processButton = (Button) dialog.getDialogPane().lookupButton(processButtonType);
                processButton.setDisable(true);
            }
        };

        efectivoField.textProperty().addListener((obs, oldVal, newVal) -> updateTotal.run());
        digitalField.textProperty().addListener((obs, oldVal, newVal) -> updateTotal.run());

        // Separador visual
        Separator separator1 = new Separator();
        Separator separator2 = new Separator();

        contentBox.getChildren().addAll(
            titleLabel,
            infoBox,
            separator1,
            efectivoLabel, efectivoField,
            digitalLabel, digitalField,
            detallesEfectivoLabel, detallesEfectivoField,
            detallesDigitalLabel, detallesDigitalField,
            separator2,
            totalLabel
        );

        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-money-bill-wave");

        // Initially disable process button
        Button processButton = (Button) dialog.getDialogPane().lookupButton(processButtonType);
        processButton.setDisable(true);

        // Run initial validation
        updateTotal.run();

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == processButtonType) {
                try {
                    String efectivoText = efectivoField.getText().trim();
                    String digitalText = digitalField.getText().trim();

                    double efectivo = efectivoText.isEmpty() ? 0.0 : Double.parseDouble(efectivoText);
                    double digital = digitalText.isEmpty() ? 0.0 : Double.parseDouble(digitalText);

                    return new PaymentData(
                        efectivo > 0 ? efectivo : null,
                        digital > 0 ? digital : null,
                        detallesEfectivoField.getText().trim().isEmpty() ? null : detallesEfectivoField.getText().trim(),
                        detallesDigitalField.getText().trim().isEmpty() ? null : detallesDigitalField.getText().trim()
                    );
                } catch (NumberFormatException e) {
                    return null;
                }
            }
            return null;
        });

        // Configurar focus en el primer campo
        Platform.runLater(() -> {
            efectivoField.requestFocus();
            efectivoField.selectAll();
        });

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo para crear una nueva CajaGui cuando no hay cajas disponibles.
     */
    public Optional<CreateCajaData> showCreateCajaDialog() {
        Dialog<CreateCajaData> dialog = new Dialog<>();
        dialog.setTitle("Crear Nueva Caja");

        // Crear botones
        ButtonType createButtonType = new ButtonType("Crear Caja", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(createButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));
        contentBox.setAlignment(Pos.CENTER);

        // Título
        Label titleLabel = new Label("No hay cajas disponibles");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 16px; -fx-font-weight: 600;");

        // Mensaje explicativo
        Label messageLabel = new Label("Para continuar, debe crear una nueva caja.");
        messageLabel.setStyle("-fx-text-fill: #a0a0a0; -fx-font-size: 12px;");
        messageLabel.setWrapText(true);

        // Campo para el nombre de la caja
        Label nameLabel = new Label("Nombre de la nueva caja:");
        nameLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextField nameField = new TextField();
        nameField.setPromptText("Ej: Caja Principal, Caja 1, etc.");
        nameField.setPrefWidth(300);
        nameField.setMaxWidth(Double.MAX_VALUE);

        // Campo opcional para configuración GUI
        Label configLabel = new Label("Configuración inicial (opcional):");
        configLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextArea configArea = new TextArea();
        configArea.setPromptText("Configuración JSON opcional para la interfaz...");
        configArea.setPrefRowCount(3);
        configArea.setPrefWidth(300);
        configArea.setMaxWidth(Double.MAX_VALUE);

        // Separador visual
        Separator separator = new Separator();

        contentBox.getChildren().addAll(
            titleLabel,
            messageLabel,
            separator,
            nameLabel, nameField,
            configLabel, configArea
        );

        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-plus-circle");

        // Enable/disable create button based on name field
        Button createButton = (Button) dialog.getDialogPane().lookupButton(createButtonType);
        createButton.setDisable(true);
        nameField.textProperty().addListener(
            (obs, oldText, newText) -> createButton.setDisable(newText == null || newText.trim().isEmpty())
        );

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == createButtonType) {
                String name = nameField.getText().trim();
                String config = configArea.getText().trim();
                if (!name.isEmpty()) {
                    return new CreateCajaData(name, config.isEmpty() ? null : config);
                }
            }
            return null;
        });

        // Configurar focus en el campo de nombre
        Platform.runLater(() -> {
            nameField.requestFocus();
        });

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo para inicializar una CajaDineroEfectivo.
     */
    public Optional<InitializeCajaEfectivoData> showInitializeCajaEfectivoDialog() {
        Dialog<InitializeCajaEfectivoData> dialog = new Dialog<>();
        dialog.setTitle("Inicializar Caja de Efectivo");

        // Crear botones
        ButtonType initButtonType = new ButtonType("Inicializar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(initButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));

        // Título
        Label titleLabel = new Label("Inicializar Caja de Efectivo");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 16px; -fx-font-weight: 600;");

        // Campo para el nombre
        Label nameLabel = new Label("Nombre de la caja de efectivo:");
        nameLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextField nameField = new TextField();
        nameField.setPromptText("Ej: Caja Efectivo Principal");
        nameField.setPrefWidth(300);

        // Campo para monto inicial
        Label montoLabel = new Label("Monto inicial (opcional):");
        montoLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextField montoField = new TextField();
        montoField.setPromptText("0.00");
        montoField.setPrefWidth(150);

        // Sección de denominaciones
        Label denomLabel = new Label("Denominaciones iniciales (opcional):");
        denomLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        // Crear campos para cada denominación
        VBox denomBox = new VBox(8);
        denomBox.setStyle("-fx-background-color: #2a2a2a; -fx-padding: 15; -fx-border-radius: 8; -fx-background-radius: 8;");

        // Monedas
        Label monedasLabel = new Label("Monedas:");
        monedasLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 11px; -fx-font-weight: bold;");

        TextField diezCentimosField = createDenominationField("10 céntimos");
        TextField veinteCentimosField = createDenominationField("20 céntimos");
        TextField cincuentaCentimosField = createDenominationField("50 céntimos");
        TextField unSolField = createDenominationField("1 sol");
        TextField dosSolesField = createDenominationField("2 soles");
        TextField cincoSolesField = createDenominationField("5 soles");

        // Billetes
        Label billetesLabel = new Label("Billetes:");
        billetesLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 11px; -fx-font-weight: bold;");

        TextField diezSolesField = createDenominationField("10 soles");
        TextField veinteSolesField = createDenominationField("20 soles");
        TextField cincuentaSolesField = createDenominationField("50 soles");
        TextField cienSolesField = createDenominationField("100 soles");
        TextField doscientosSolesField = createDenominationField("200 soles");

        denomBox.getChildren().addAll(
            monedasLabel,
            createDenominationRow("10 céntimos:", diezCentimosField),
            createDenominationRow("20 céntimos:", veinteCentimosField),
            createDenominationRow("50 céntimos:", cincuentaCentimosField),
            createDenominationRow("1 sol:", unSolField),
            createDenominationRow("2 soles:", dosSolesField),
            createDenominationRow("5 soles:", cincoSolesField),
            new Separator(),
            billetesLabel,
            createDenominationRow("10 soles:", diezSolesField),
            createDenominationRow("20 soles:", veinteSolesField),
            createDenominationRow("50 soles:", cincuentaSolesField),
            createDenominationRow("100 soles:", cienSolesField),
            createDenominationRow("200 soles:", doscientosSolesField)
        );

        contentBox.getChildren().addAll(
            titleLabel,
            new Separator(),
            nameLabel, nameField,
            montoLabel, montoField,
            denomLabel, denomBox
        );

        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-cash-register");

        // Enable/disable init button based on name field
        Button initButton = (Button) dialog.getDialogPane().lookupButton(initButtonType);
        initButton.setDisable(true);
        nameField.textProperty().addListener(
            (obs, oldText, newText) -> initButton.setDisable(newText == null || newText.trim().isEmpty())
        );

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == initButtonType) {
                String name = nameField.getText().trim();
                if (!name.isEmpty()) {
                    try {
                        Double monto = parseDoubleOrNull(montoField.getText());
                        return new InitializeCajaEfectivoData(
                            name, monto,
                            parseIntegerOrNull(diezCentimosField.getText()),
                            parseIntegerOrNull(veinteCentimosField.getText()),
                            parseIntegerOrNull(cincuentaCentimosField.getText()),
                            parseIntegerOrNull(unSolField.getText()),
                            parseIntegerOrNull(dosSolesField.getText()),
                            parseIntegerOrNull(cincoSolesField.getText()),
                            parseIntegerOrNull(diezSolesField.getText()),
                            parseIntegerOrNull(veinteSolesField.getText()),
                            parseIntegerOrNull(cincuentaSolesField.getText()),
                            parseIntegerOrNull(cienSolesField.getText()),
                            parseIntegerOrNull(doscientosSolesField.getText())
                        );
                    } catch (NumberFormatException e) {
                        alertUtil.showError("Error en los datos ingresados. Verifique que los números sean válidos.");
                        return null;
                    }
                }
            }
            return null;
        });

        // Configurar focus en el campo de nombre
        Platform.runLater(() -> {
            nameField.requestFocus();
        });

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo para cerrar una CajaDineroEfectivo.
     */
    public Optional<CloseCajaEfectivoData> showCloseCajaEfectivoDialog() {
        Dialog<CloseCajaEfectivoData> dialog = new Dialog<>();
        dialog.setTitle("Cerrar Caja de Efectivo");

        // Crear botones
        ButtonType closeButtonType = new ButtonType("Cerrar Caja", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(closeButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));

        // Título
        Label titleLabel = new Label("Cerrar Caja de Efectivo");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 16px; -fx-font-weight: 600;");

        // Mensaje explicativo
        Label messageLabel = new Label("Ingrese las cantidades finales de cada denominación:");
        messageLabel.setStyle("-fx-text-fill: #a0a0a0; -fx-font-size: 12px;");
        messageLabel.setWrapText(true);

        // Sección de denominaciones finales
        VBox denomBox = new VBox(8);
        denomBox.setStyle("-fx-background-color: #2a2a2a; -fx-padding: 15; -fx-border-radius: 8; -fx-background-radius: 8;");

        // Monedas
        Label monedasLabel = new Label("Monedas:");
        monedasLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 11px; -fx-font-weight: bold;");

        TextField diezCentimosField = createDenominationField("10 céntimos");
        TextField veinteCentimosField = createDenominationField("20 céntimos");
        TextField cincuentaCentimosField = createDenominationField("50 céntimos");
        TextField unSolField = createDenominationField("1 sol");
        TextField dosSolesField = createDenominationField("2 soles");
        TextField cincoSolesField = createDenominationField("5 soles");

        // Billetes
        Label billetesLabel = new Label("Billetes:");
        billetesLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 11px; -fx-font-weight: bold;");

        TextField diezSolesField = createDenominationField("10 soles");
        TextField veinteSolesField = createDenominationField("20 soles");
        TextField cincuentaSolesField = createDenominationField("50 soles");
        TextField cienSolesField = createDenominationField("100 soles");
        TextField doscientosSolesField = createDenominationField("200 soles");

        denomBox.getChildren().addAll(
            monedasLabel,
            createDenominationRow("10 céntimos:", diezCentimosField),
            createDenominationRow("20 céntimos:", veinteCentimosField),
            createDenominationRow("50 céntimos:", cincuentaCentimosField),
            createDenominationRow("1 sol:", unSolField),
            createDenominationRow("2 soles:", dosSolesField),
            createDenominationRow("5 soles:", cincoSolesField),
            new Separator(),
            billetesLabel,
            createDenominationRow("10 soles:", diezSolesField),
            createDenominationRow("20 soles:", veinteSolesField),
            createDenominationRow("50 soles:", cincuentaSolesField),
            createDenominationRow("100 soles:", cienSolesField),
            createDenominationRow("200 soles:", doscientosSolesField)
        );

        contentBox.getChildren().addAll(
            titleLabel,
            messageLabel,
            new Separator(),
            denomBox
        );

        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-cash-register");

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == closeButtonType) {
                try {
                    return new CloseCajaEfectivoData(
                        parseIntegerOrNull(diezCentimosField.getText()),
                        parseIntegerOrNull(veinteCentimosField.getText()),
                        parseIntegerOrNull(cincuentaCentimosField.getText()),
                        parseIntegerOrNull(unSolField.getText()),
                        parseIntegerOrNull(dosSolesField.getText()),
                        parseIntegerOrNull(cincoSolesField.getText()),
                        parseIntegerOrNull(diezSolesField.getText()),
                        parseIntegerOrNull(veinteSolesField.getText()),
                        parseIntegerOrNull(cincuentaSolesField.getText()),
                        parseIntegerOrNull(cienSolesField.getText()),
                        parseIntegerOrNull(doscientosSolesField.getText())
                    );
                } catch (NumberFormatException e) {
                    alertUtil.showError("Error en los datos ingresados. Verifique que los números sean válidos.");
                    return null;
                }
            }
            return null;
        });

        // Configurar focus en el primer campo
        Platform.runLater(() -> {
            diezCentimosField.requestFocus();
        });

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo para inicializar una CajaDineroDigital.
     */
    public Optional<InitializeCajaDigitalData> showInitializeCajaDigitalDialog() {
        Dialog<InitializeCajaDigitalData> dialog = new Dialog<>();
        dialog.setTitle("Inicializar Caja Digital");

        // Crear botones
        ButtonType initButtonType = new ButtonType("Inicializar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(initButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));

        // Título
        Label titleLabel = new Label("Inicializar Caja Digital");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 16px; -fx-font-weight: 600;");

        // Campo para cuenta digital
        Label cuentaLabel = new Label("Cuenta digital asignada:");
        cuentaLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextField cuentaField = new TextField();
        cuentaField.setPromptText("Ej: Cuenta BCP Digital, Yape, Plin, etc.");
        cuentaField.setPrefWidth(300);

        // Campo para monto inicial
        Label montoLabel = new Label("Monto inicial digital (opcional):");
        montoLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextField montoField = new TextField();
        montoField.setPromptText("0.00");
        montoField.setPrefWidth(150);

        // Información adicional
        VBox infoBox = new VBox(4);
        infoBox.setStyle("-fx-background-color: #2a2a2a; -fx-padding: 15; -fx-border-radius: 8; -fx-background-radius: 8;");

        Label infoLabel = new Label("ℹ️ Información:");
        infoLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 11px; -fx-font-weight: bold;");

        Label info1 = new Label("• La cuenta digital será usada para recibir pagos digitales");
        info1.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 10px;");

        Label info2 = new Label("• El monto inicial es opcional y representa el saldo inicial");
        info2.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 10px;");

        infoBox.getChildren().addAll(infoLabel, info1, info2);

        contentBox.getChildren().addAll(
            titleLabel,
            new Separator(),
            cuentaLabel, cuentaField,
            montoLabel, montoField,
            infoBox
        );

        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-credit-card");

        // Enable/disable init button based on cuenta field
        Button initButton = (Button) dialog.getDialogPane().lookupButton(initButtonType);
        initButton.setDisable(true);
        cuentaField.textProperty().addListener(
            (obs, oldText, newText) -> initButton.setDisable(newText == null || newText.trim().isEmpty())
        );

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == initButtonType) {
                String cuenta = cuentaField.getText().trim();
                if (!cuenta.isEmpty()) {
                    try {
                        Double monto = parseDoubleOrNull(montoField.getText());
                        return new InitializeCajaDigitalData(cuenta, monto);
                    } catch (NumberFormatException e) {
                        alertUtil.showError("Error en el monto ingresado. Verifique que sea un número válido.");
                        return null;
                    }
                }
            }
            return null;
        });

        // Configurar focus en el campo de cuenta
        Platform.runLater(() -> {
            cuentaField.requestFocus();
        });

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo para cerrar una CajaDineroDigital.
     */
    public Optional<CloseCajaDigitalData> showCloseCajaDigitalDialog() {
        Dialog<CloseCajaDigitalData> dialog = new Dialog<>();
        dialog.setTitle("Cerrar Caja Digital");

        // Crear botones
        ButtonType closeButtonType = new ButtonType("Cerrar Caja", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(closeButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));

        // Título
        Label titleLabel = new Label("Cerrar Caja Digital");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 16px; -fx-font-weight: 600;");

        // Mensaje explicativo
        Label messageLabel = new Label("Ingrese el monto final declarado en la cuenta digital:");
        messageLabel.setStyle("-fx-text-fill: #a0a0a0; -fx-font-size: 12px;");
        messageLabel.setWrapText(true);

        // Campo para monto final
        Label montoLabel = new Label("Monto final declarado:");
        montoLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextField montoField = new TextField();
        montoField.setPromptText("0.00");
        montoField.setPrefWidth(200);

        // Información adicional
        VBox infoBox = new VBox(4);
        infoBox.setStyle("-fx-background-color: #2a2a2a; -fx-padding: 15; -fx-border-radius: 8; -fx-background-radius: 8;");

        Label infoLabel = new Label("ℹ️ Información:");
        infoLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 11px; -fx-font-weight: bold;");

        Label info1 = new Label("• Ingrese el saldo final que aparece en la cuenta digital");
        info1.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 10px;");

        Label info2 = new Label("• Este monto será usado para el cierre de caja");
        info2.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 10px;");

        infoBox.getChildren().addAll(infoLabel, info1, info2);

        contentBox.getChildren().addAll(
            titleLabel,
            messageLabel,
            new Separator(),
            montoLabel, montoField,
            infoBox
        );

        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-credit-card");

        // Enable/disable close button based on monto field
        Button closeButton = (Button) dialog.getDialogPane().lookupButton(closeButtonType);
        closeButton.setDisable(true);
        montoField.textProperty().addListener(
            (obs, oldText, newText) -> {
                try {
                    if (newText != null && !newText.trim().isEmpty()) {
                        Double.parseDouble(newText.trim());
                        closeButton.setDisable(false);
                    } else {
                        closeButton.setDisable(true);
                    }
                } catch (NumberFormatException e) {
                    closeButton.setDisable(true);
                }
            }
        );

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == closeButtonType) {
                try {
                    String montoText = montoField.getText().trim();
                    if (!montoText.isEmpty()) {
                        Double monto = Double.parseDouble(montoText);
                        return new CloseCajaDigitalData(monto);
                    }
                } catch (NumberFormatException e) {
                    alertUtil.showError("Error en el monto ingresado. Verifique que sea un número válido.");
                    return null;
                }
            }
            return null;
        });

        // Configurar focus en el campo de monto
        Platform.runLater(() -> {
            montoField.requestFocus();
        });

        return dialog.showAndWait();
    }

    /**
     * Muestra el diálogo para ingresar el ID de un CobroDineroProgramado.
     */
    public Optional<String> showLeerVentaDialog() {
        Dialog<String> dialog = new Dialog<>();
        dialog.setTitle("Leer Venta");

        // Crear botones
        ButtonType okButtonType = new ButtonType("Buscar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(okButtonType, cancelButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));

        // Título
        Label titleLabel = new Label("Buscar Cobro por ID");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 16px; -fx-font-weight: 600;");

        // Mensaje explicativo
        Label messageLabel = new Label("Ingrese el ID del CobroDineroProgramado que desea buscar:");
        messageLabel.setStyle("-fx-text-fill: #a0a0a0; -fx-font-size: 12px;");
        messageLabel.setWrapText(true);

        // Campo para el ID
        Label idLabel = new Label("ID del Cobro:");
        idLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 12px; -fx-font-weight: 600;");

        TextField idField = new TextField();
        idField.setPromptText("Ej: 12345678-1234-1234-1234-123456789012");
        idField.setPrefWidth(400);
        idField.setMaxWidth(Double.MAX_VALUE);

        // Información adicional
        VBox infoBox = new VBox(4);
        infoBox.setStyle("-fx-background-color: #2a2a2a; -fx-padding: 15; -fx-border-radius: 8; -fx-background-radius: 8;");

        Label infoLabel = new Label("ℹ️ Información:");
        infoLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 11px; -fx-font-weight: bold;");

        Label info1 = new Label("• Puede ingresar el ID completo o los primeros 8 caracteres");
        info1.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 10px;");

        Label info2 = new Label("• El sistema buscará en todos los cobros pendientes");
        info2.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 10px;");

        Label info3 = new Label("• Si se encuentra, se seleccionará automáticamente");
        info3.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 10px;");

        infoBox.getChildren().addAll(infoLabel, info1, info2, info3);

        contentBox.getChildren().addAll(
            titleLabel,
            messageLabel,
            new Separator(),
            idLabel, idField,
            infoBox
        );

        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-search");

        // Enable/disable search button based on ID field
        Button searchButton = (Button) dialog.getDialogPane().lookupButton(okButtonType);
        searchButton.setDisable(true);
        idField.textProperty().addListener(
            (obs, oldText, newText) -> searchButton.setDisable(newText == null || newText.trim().isEmpty())
        );

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == okButtonType) {
                String idText = idField.getText().trim();
                if (!idText.isEmpty()) {
                    return idText;
                }
            }
            return null;
        });

        // Configurar focus en el campo de ID
        Platform.runLater(() -> {
            idField.requestFocus();
        });

        return dialog.showAndWait();
    }

    // Utility methods for dialog creation

    /**
     * Crea un TextField para denominaciones con estilo consistente.
     */
    private TextField createDenominationField(String denomination) {
        TextField field = new TextField();
        field.setPromptText("0");
        field.setPrefWidth(80);
        field.setMaxWidth(80);
        return field;
    }

    /**
     * Crea una fila con label y campo para denominaciones.
     */
    private javafx.scene.layout.HBox createDenominationRow(String labelText, TextField field) {
        javafx.scene.layout.HBox row = new javafx.scene.layout.HBox(10);
        row.setAlignment(Pos.CENTER_LEFT);

        Label label = new Label(labelText);
        label.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 10px;");
        label.setMinWidth(80);

        row.getChildren().addAll(label, field);
        return row;
    }

    /**
     * Parsea un String a Double, retorna null si está vacío o es inválido.
     */
    private Double parseDoubleOrNull(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }
        try {
            return Double.parseDouble(text.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * Parsea un String a Integer, retorna null si está vacío o es inválido.
     */
    private Integer parseIntegerOrNull(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(text.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * Aplica estilos modernos a un diálogo.
     */
    private void applyDialogStyles(Dialog<?> dialog) {
        // Hacer el diálogo sin decoraciones para un diseño moderno
        dialog.initStyle(StageStyle.UNDECORATED);

        // Aplicar estilos CSS
        dialog.getDialogPane().getStylesheets().addAll(
                getClass().getResource(STYLESHEET_PATH).toExternalForm(),
                getClass().getResource(SEARCH_PRODUCT_STYLESHEET_PATH).toExternalForm()
        );

        // Hacer el diálogo arrastrable
        makeDraggable(dialog);
    }

    /**
     * Configura un icono para el diálogo.
     */
    private void setDialogIcon(Dialog<?> dialog, String iconClass) {
        // Implementación básica - se puede expandir con iconos reales
        log.debug("Configurando icono {} para diálogo", iconClass);
    }

    /**
     * Hace que el diálogo sea arrastrable.
     */
    private void makeDraggable(Dialog<?> dialog) {
        final double[] xOffset = {0};
        final double[] yOffset = {0};

        dialog.getDialogPane().setOnMousePressed(event -> {
            xOffset[0] = event.getSceneX();
            yOffset[0] = event.getSceneY();
        });

        dialog.getDialogPane().setOnMouseDragged(event -> {
            dialog.setX(event.getScreenX() - xOffset[0]);
            dialog.setY(event.getScreenY() - yOffset[0]);
        });
    }

    /**
     * Clase para encapsular los datos de pago.
     */
    public static class PaymentData {
        private final Double montoEfectivo;
        private final Double montoDigital;
        private final String detallesEfectivo;
        private final String detallesDigital;

        public PaymentData(Double montoEfectivo, Double montoDigital, String detallesEfectivo, String detallesDigital) {
            this.montoEfectivo = montoEfectivo;
            this.montoDigital = montoDigital;
            this.detallesEfectivo = detallesEfectivo;
            this.detallesDigital = detallesDigital;
        }

        public Double getMontoEfectivo() { return montoEfectivo; }
        public Double getMontoDigital() { return montoDigital; }
        public String getDetallesEfectivo() { return detallesEfectivo; }
        public String getDetallesDigital() { return detallesDigital; }
    }

    /**
     * Clase para encapsular los datos de creación de caja.
     */
    public static class CreateCajaData {
        private final String nombreCaja;
        private final String guiConfig;

        public CreateCajaData(String nombreCaja, String guiConfig) {
            this.nombreCaja = nombreCaja;
            this.guiConfig = guiConfig;
        }

        public String getNombreCaja() { return nombreCaja; }
        public String getGuiConfig() { return guiConfig; }
    }

    /**
     * Clase para encapsular el resultado de la selección de caja.
     */
    public static class CajaSelectionResult {
        private final CajaGui selectedCaja;
        private final boolean wantsToCreate;

        public CajaSelectionResult(CajaGui selectedCaja, boolean wantsToCreate) {
            this.selectedCaja = selectedCaja;
            this.wantsToCreate = wantsToCreate;
        }

        public CajaGui getSelectedCaja() { return selectedCaja; }
        public boolean isWantsToCreate() { return wantsToCreate; }
    }

    /**
     * Clase para encapsular los datos de inicialización de caja de efectivo.
     */
    public static class InitializeCajaEfectivoData {
        private final String nombre;
        private final Double montoInicial;
        private final Integer inicioDiezCentimos;
        private final Integer inicioVeinteCentimos;
        private final Integer inicioCincuentaCentimos;
        private final Integer inicioUnSol;
        private final Integer inicioDosSoles;
        private final Integer inicioCincoSoles;
        private final Integer inicioDiezSoles;
        private final Integer inicioVeinteSoles;
        private final Integer inicioCincuentaSoles;
        private final Integer inicioCienSoles;
        private final Integer inicioDoscientosSoles;

        public InitializeCajaEfectivoData(String nombre, Double montoInicial,
                                        Integer inicioDiezCentimos, Integer inicioVeinteCentimos, Integer inicioCincuentaCentimos,
                                        Integer inicioUnSol, Integer inicioDosSoles, Integer inicioCincoSoles,
                                        Integer inicioDiezSoles, Integer inicioVeinteSoles, Integer inicioCincuentaSoles,
                                        Integer inicioCienSoles, Integer inicioDoscientosSoles) {
            this.nombre = nombre;
            this.montoInicial = montoInicial;
            this.inicioDiezCentimos = inicioDiezCentimos;
            this.inicioVeinteCentimos = inicioVeinteCentimos;
            this.inicioCincuentaCentimos = inicioCincuentaCentimos;
            this.inicioUnSol = inicioUnSol;
            this.inicioDosSoles = inicioDosSoles;
            this.inicioCincoSoles = inicioCincoSoles;
            this.inicioDiezSoles = inicioDiezSoles;
            this.inicioVeinteSoles = inicioVeinteSoles;
            this.inicioCincuentaSoles = inicioCincuentaSoles;
            this.inicioCienSoles = inicioCienSoles;
            this.inicioDoscientosSoles = inicioDoscientosSoles;
        }

        public String getNombre() { return nombre; }
        public Double getMontoInicial() { return montoInicial; }
        public Integer getInicioDiezCentimos() { return inicioDiezCentimos; }
        public Integer getInicioVeinteCentimos() { return inicioVeinteCentimos; }
        public Integer getInicioCincuentaCentimos() { return inicioCincuentaCentimos; }
        public Integer getInicioUnSol() { return inicioUnSol; }
        public Integer getInicioDosSoles() { return inicioDosSoles; }
        public Integer getInicioCincoSoles() { return inicioCincoSoles; }
        public Integer getInicioDiezSoles() { return inicioDiezSoles; }
        public Integer getInicioVeinteSoles() { return inicioVeinteSoles; }
        public Integer getInicioCincuentaSoles() { return inicioCincuentaSoles; }
        public Integer getInicioCienSoles() { return inicioCienSoles; }
        public Integer getInicioDoscientosSoles() { return inicioDoscientosSoles; }
    }

    /**
     * Clase para encapsular los datos de cierre de caja de efectivo.
     */
    public static class CloseCajaEfectivoData {
        private final Integer cierreDiezCentimos;
        private final Integer cierreVeinteCentimos;
        private final Integer cierreCincuentaCentimos;
        private final Integer cierreUnSol;
        private final Integer cierreDosSoles;
        private final Integer cierreCincoSoles;
        private final Integer cierreDiezSoles;
        private final Integer cierreVeinteSoles;
        private final Integer cierreCincuentaSoles;
        private final Integer cierreCienSoles;
        private final Integer cierreDoscientosSoles;

        public CloseCajaEfectivoData(Integer cierreDiezCentimos, Integer cierreVeinteCentimos, Integer cierreCincuentaCentimos,
                                   Integer cierreUnSol, Integer cierreDosSoles, Integer cierreCincoSoles,
                                   Integer cierreDiezSoles, Integer cierreVeinteSoles, Integer cierreCincuentaSoles,
                                   Integer cierreCienSoles, Integer cierreDoscientosSoles) {
            this.cierreDiezCentimos = cierreDiezCentimos;
            this.cierreVeinteCentimos = cierreVeinteCentimos;
            this.cierreCincuentaCentimos = cierreCincuentaCentimos;
            this.cierreUnSol = cierreUnSol;
            this.cierreDosSoles = cierreDosSoles;
            this.cierreCincoSoles = cierreCincoSoles;
            this.cierreDiezSoles = cierreDiezSoles;
            this.cierreVeinteSoles = cierreVeinteSoles;
            this.cierreCincuentaSoles = cierreCincuentaSoles;
            this.cierreCienSoles = cierreCienSoles;
            this.cierreDoscientosSoles = cierreDoscientosSoles;
        }

        public Integer getCierreDiezCentimos() { return cierreDiezCentimos; }
        public Integer getCierreVeinteCentimos() { return cierreVeinteCentimos; }
        public Integer getCierreCincuentaCentimos() { return cierreCincuentaCentimos; }
        public Integer getCierreUnSol() { return cierreUnSol; }
        public Integer getCierreDosSoles() { return cierreDosSoles; }
        public Integer getCierreCincoSoles() { return cierreCincoSoles; }
        public Integer getCierreDiezSoles() { return cierreDiezSoles; }
        public Integer getCierreVeinteSoles() { return cierreVeinteSoles; }
        public Integer getCierreCincuentaSoles() { return cierreCincuentaSoles; }
        public Integer getCierreCienSoles() { return cierreCienSoles; }
        public Integer getCierreDoscientosSoles() { return cierreDoscientosSoles; }
    }

    /**
     * Clase para encapsular los datos de inicialización de caja digital.
     */
    public static class InitializeCajaDigitalData {
        private final String cuentaDigitalAsignada;
        private final Double montoInicialDigital;

        public InitializeCajaDigitalData(String cuentaDigitalAsignada, Double montoInicialDigital) {
            this.cuentaDigitalAsignada = cuentaDigitalAsignada;
            this.montoInicialDigital = montoInicialDigital;
        }

        public String getCuentaDigitalAsignada() { return cuentaDigitalAsignada; }
        public Double getMontoInicialDigital() { return montoInicialDigital; }
    }

    /**
     * Clase para encapsular los datos de cierre de caja digital.
     */
    public static class CloseCajaDigitalData {
        private final Double cierreDigitalDeclarado;

        public CloseCajaDigitalData(Double cierreDigitalDeclarado) {
            this.cierreDigitalDeclarado = cierreDigitalDeclarado;
        }

        public Double getCierreDigitalDeclarado() { return cierreDigitalDeclarado; }
    }
}
