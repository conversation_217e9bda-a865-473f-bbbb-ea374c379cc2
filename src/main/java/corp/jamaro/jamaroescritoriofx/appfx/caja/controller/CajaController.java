package corp.jamaro.jamaroescritoriofx.appfx.caja.controller;

import corp.jamaro.jamaroescritoriofx.appfx.caja.model.gui.CajaGui;
import corp.jamaro.jamaroescritoriofx.appfx.caja.service.CajaGuiService;
import corp.jamaro.jamaroescritoriofx.appfx.caja.util.CajaDialogUtil;
import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import corp.jamaro.jamaroescritoriofx.util.LoadingUtil;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.control.MenuBar;
import javafx.scene.control.MenuItem;
import javafx.scene.control.ProgressIndicator;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

/**
 * Controlador principal para la gestión de caja.
 * Maneja la selección de CajaGui y la visualización de cobros pendientes.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class CajaController extends BaseController {

    private final CajaGuiService cajaGuiService;
    private final CajaDialogUtil cajaDialogUtil;
    private final LoadingUtil loadingUtil;

    @FXML
    private StackPane mainStackPane;

    @FXML
    private ProgressIndicator loadingIndicator;

    @FXML
    private MenuBar menuBar;

    @FXML
    private MenuItem menuInicializarEfectivo;

    @FXML
    private MenuItem menuInicializarDigital;

    @FXML
    private MenuItem menuCerrarEfectivo;

    @FXML
    private MenuItem menuCerrarDigital;

    @FXML
    private VBox contentContainer;

    @FXML
    private Label lblNombreCaja;

    @FXML
    private Label lblEstadoEfectivo;

    @FXML
    private Label lblEstadoDigital;

    @FXML
    private VBox salesContainer;

    // Estado del controlador
    private CajaGui currentCajaGui;
    private final ObservableList<Sale> salesPorCobrar = FXCollections.observableArrayList();
    private Disposable salesSubscription;
    private Disposable cajaGuiSubscription;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        log.info("Inicializando CajaController");

        // Configurar estado inicial de los menús
        updateMenuState();

        // Configurar eventos del menú
        setupMenuEvents();

        // Inicializar la selección de CajaGui
        initializeCajaGuiSelection();
    }

    /**
     * Configura los eventos del menú bar
     */
    private void setupMenuEvents() {
        menuInicializarEfectivo.setOnAction(e -> handleInicializarEfectivo());
        menuInicializarDigital.setOnAction(e -> handleInicializarDigital());
        menuCerrarEfectivo.setOnAction(e -> handleCerrarEfectivo());
        menuCerrarDigital.setOnAction(e -> handleCerrarDigital());
    }

    /**
     * Actualiza el estado de los elementos del menú según la CajaGui actual
     */
    private void updateMenuState() {
        boolean hasCajaGui = currentCajaGui != null;
        boolean hasEfectivo = hasCajaGui && currentCajaGui.getCajaDineroEfectivo() != null;
        boolean hasDigital = hasCajaGui && currentCajaGui.getCajaDineroDigital() != null;

        menuInicializarEfectivo.setDisable(!hasCajaGui || hasEfectivo);
        menuInicializarDigital.setDisable(!hasCajaGui || hasDigital);
        menuCerrarEfectivo.setDisable(!hasEfectivo);
        menuCerrarDigital.setDisable(!hasDigital);
    }

    /**
     * Inicializa la selección de CajaGui mostrando el diálogo correspondiente
     */
    private void initializeCajaGuiSelection() {
        log.info("Iniciando selección de CajaGui");

        loadingUtil.subscribeWithLoading(
            cajaGuiService.getAllCajaGuiOrderedByCreatedAt().collectList(),
            loadingIndicator,
            this::handleCajaGuiListReceived,
            error -> {
                log.error("Error al obtener lista de CajaGui", error);
                Platform.runLater(() -> {
                    // Mostrar error y permitir crear nueva caja
                    showCreateCajaDialog();
                });
            }
        );
    }

    /**
     * Maneja la lista de CajaGui recibida del servidor
     */
    private void handleCajaGuiListReceived(List<CajaGui> cajaGuiList) {
        Platform.runLater(() -> {
            if (cajaGuiList.isEmpty()) {
                log.info("No hay CajaGui disponibles, mostrando diálogo de creación");
                showCreateCajaDialog();
            } else {
                log.info("Mostrando diálogo de selección con {} CajaGui disponibles", cajaGuiList.size());
                showCajaSelectionDialog(cajaGuiList);
            }
        });
    }

    /**
     * Muestra el diálogo de selección de CajaGui
     */
    private void showCajaSelectionDialog(List<CajaGui> cajaGuiList) {
        Optional<CajaDialogUtil.CajaSelectionResult> result = cajaDialogUtil.showCajaSelectionDialog(cajaGuiList);

        result.ifPresentOrElse(
            selectionResult -> {
                if (selectionResult.isCreateNew()) {
                    showCreateCajaDialog();
                } else {
                    CajaGui selectedCaja = selectionResult.getSelectedCajaGui();
                    log.info("CajaGui seleccionada: {} - {}", selectedCaja.getId(), selectedCaja.getNombreCaja());
                    initializeCajaGui(selectedCaja);
                }
            },
            () -> {
                log.info("Selección de CajaGui cancelada");
                // Podríamos cerrar la ventana o mostrar un mensaje
            }
        );
    }

    /**
     * Muestra el diálogo de creación de nueva CajaGui
     */
    private void showCreateCajaDialog() {
        Optional<CajaDialogUtil.CreateCajaData> result = cajaDialogUtil.showCreateCajaDialog();

        result.ifPresentOrElse(
            createData -> {
                log.info("Creando nueva CajaGui: {}", createData.nombreCaja());
                createNewCajaGui(createData.nombreCaja(), createData.guiConfig());
            },
            () -> {
                log.info("Creación de CajaGui cancelada");
                // Volver a mostrar selección si hay cajas disponibles
                initializeCajaGuiSelection();
            }
        );
    }

    /**
     * Crea una nueva CajaGui
     */
    private void createNewCajaGui(String nombreCaja, String guiConfig) {
        loadingUtil.subscribeWithLoading(
            cajaGuiService.createCajaGui(nombreCaja, guiConfig),
            loadingIndicator,
            this::initializeCajaGui,
            error -> {
                log.error("Error al crear CajaGui", error);
                Platform.runLater(() -> {
                    // Mostrar error y volver al diálogo de creación
                    showCreateCajaDialog();
                });
            }
        );
    }

    /**
     * Inicializa la CajaGui seleccionada y se suscribe a sus cambios
     */
    private void initializeCajaGui(CajaGui cajaGui) {
        log.info("Inicializando CajaGui: {} - {}", cajaGui.getId(), cajaGui.getNombreCaja());

        this.currentCajaGui = cajaGui;

        Platform.runLater(() -> {
            updateMenuState();
            updateCajaInfoUI();
        });

        // Suscribirse a cambios de la CajaGui
        subscribeToCurrentCajaGui();

        // Suscribirse a ventas por cobrar
        subscribeToSalesPorCobrar();
    }

    /**
     * Se suscribe a los cambios de la CajaGui actual
     */
    private void subscribeToCurrentCajaGui() {
        if (currentCajaGui == null) {
            log.warn("No se puede suscribir a CajaGui: currentCajaGui es null");
            return;
        }

        // Cancelar suscripción anterior si existe
        if (cajaGuiSubscription != null && !cajaGuiSubscription.isDisposed()) {
            cajaGuiSubscription.dispose();
        }

        cajaGuiSubscription = cajaGuiService.subscribeToCurrentCajaGui(currentCajaGui.getId())
            .subscribe(
                updatedCajaGui -> {
                    log.debug("Recibida actualización de CajaGui: {}", updatedCajaGui.getNombreCaja());
                    Platform.runLater(() -> {
                        this.currentCajaGui = updatedCajaGui;
                        updateMenuState();
                        updateCajaInfoUI();
                    });
                },
                error -> log.error("Error en suscripción a CajaGui", error)
            );
    }

    /**
     * Se suscribe a las ventas por cobrar
     */
    private void subscribeToSalesPorCobrar() {
        // Cancelar suscripción anterior si existe
        if (salesSubscription != null && !salesSubscription.isDisposed()) {
            salesSubscription.dispose();
        }

        salesSubscription = cajaGuiService.subscribeToSalesPorCobrar()
            .subscribe(
                salesList -> {
                    log.debug("Recibidas {} ventas por cobrar", salesList.size());
                    Platform.runLater(() -> {
                        salesPorCobrar.setAll(salesList);
                        updateSalesUI();
                    });
                },
                error -> log.error("Error en suscripción a ventas por cobrar", error)
            );
    }

    // Métodos para manejar eventos del menú

    /**
     * Maneja la inicialización de CajaDineroEfectivo
     */
    private void handleInicializarEfectivo() {
        if (currentCajaGui == null) {
            log.warn("No se puede inicializar efectivo: no hay CajaGui seleccionada");
            return;
        }

        log.info("Iniciando proceso de inicialización de CajaDineroEfectivo");
        cajaDialogUtil.showInitializeCajaDineroEfectivoDialog(currentCajaGui.getId())
            .ifPresent(this::procesarInicializacionEfectivo);
    }

    /**
     * Maneja la inicialización de CajaDineroDigital
     */
    private void handleInicializarDigital() {
        if (currentCajaGui == null) {
            log.warn("No se puede inicializar digital: no hay CajaGui seleccionada");
            return;
        }

        log.info("Iniciando proceso de inicialización de CajaDineroDigital");
        cajaDialogUtil.showInitializeCajaDineroDigitalDialog(currentCajaGui.getId())
            .ifPresent(this::procesarInicializacionDigital);
    }

    /**
     * Maneja el cierre de CajaDineroEfectivo
     */
    private void handleCerrarEfectivo() {
        if (currentCajaGui == null || currentCajaGui.getCajaDineroEfectivo() == null) {
            log.warn("No se puede cerrar efectivo: no hay CajaDineroEfectivo disponible");
            return;
        }

        log.info("Iniciando proceso de cierre de CajaDineroEfectivo");
        cajaDialogUtil.showCloseCajaDineroEfectivoDialog(
            currentCajaGui.getId(),
            currentCajaGui.getCajaDineroEfectivo().getId()
        ).ifPresent(this::procesarCierreEfectivo);
    }

    /**
     * Maneja el cierre de CajaDineroDigital
     */
    private void handleCerrarDigital() {
        if (currentCajaGui == null || currentCajaGui.getCajaDineroDigital() == null) {
            log.warn("No se puede cerrar digital: no hay CajaDineroDigital disponible");
            return;
        }

        log.info("Iniciando proceso de cierre de CajaDineroDigital");
        cajaDialogUtil.showCloseCajaDineroDigitalDialog(
            currentCajaGui.getId(),
            currentCajaGui.getCajaDineroDigital().getId()
        ).ifPresent(this::procesarCierreDigital);
    }

    // Métodos para procesar las operaciones con el servidor

    /**
     * Procesa la inicialización de CajaDineroEfectivo
     */
    private void procesarInicializacionEfectivo(CajaDialogUtil.InitializeCajaDineroEfectivoData data) {
        log.info("Procesando inicialización de CajaDineroEfectivo");

        loadingUtil.subscribeWithLoading(
            cajaGuiService.initializeCajaDineroEfectivo(
                data.cajaGuiId(), data.nombre(), data.montoInicialEfectivo(),
                data.diezCentimos(), data.veinteCentimos(), data.cincuentaCentimos(),
                data.unSol(), data.dosSoles(), data.cincoSoles(),
                data.diezSoles(), data.veinteSoles(), data.cincuentaSoles(),
                data.cienSoles(), data.doscientosSoles()
            ),
            loadingIndicator,
            updatedCajaGui -> {
                log.info("CajaDineroEfectivo inicializada exitosamente");
                Platform.runLater(() -> {
                    this.currentCajaGui = updatedCajaGui;
                    updateMenuState();
                    updateCajaInfoUI();
                });
            },
            error -> {
                log.error("Error al inicializar CajaDineroEfectivo", error);
                // El error se mostrará automáticamente por LoadingUtil
            }
        );
    }

    /**
     * Procesa la inicialización de CajaDineroDigital
     */
    private void procesarInicializacionDigital(CajaDialogUtil.InitializeCajaDineroDigitalData data) {
        log.info("Procesando inicialización de CajaDineroDigital");

        loadingUtil.subscribeWithLoading(
            cajaGuiService.initializeCajaDineroDigital(
                data.cajaGuiId(), data.cuentaDigitalAsignada(), data.montoInicialDigital()
            ),
            loadingIndicator,
            updatedCajaGui -> {
                log.info("CajaDineroDigital inicializada exitosamente");
                Platform.runLater(() -> {
                    this.currentCajaGui = updatedCajaGui;
                    updateMenuState();
                    updateCajaInfoUI();
                });
            },
            error -> {
                log.error("Error al inicializar CajaDineroDigital", error);
                // El error se mostrará automáticamente por LoadingUtil
            }
        );
    }

    /**
     * Procesa el cierre de CajaDineroEfectivo
     */
    private void procesarCierreEfectivo(CajaDialogUtil.CloseCajaDineroEfectivoData data) {
        log.info("Procesando cierre de CajaDineroEfectivo");

        loadingUtil.subscribeWithLoading(
            cajaGuiService.closeCajaDineroEfectivo(
                data.cajaGuiId(), data.cajaDineroEfectivoId(),
                data.cierreDiezCentimos(), data.cierreVeinteCentimos(), data.cierreCincuentaCentimos(),
                data.cierreUnSol(), data.cierreDosSoles(), data.cierreCincoSoles(),
                data.cierreDiezSoles(), data.cierreVeinteSoles(), data.cierreCincuentaSoles(),
                data.cierreCienSoles(), data.cierreDoscientosSoles()
            ),
            loadingIndicator,
            updatedCajaGui -> {
                log.info("CajaDineroEfectivo cerrada exitosamente");
                Platform.runLater(() -> {
                    this.currentCajaGui = updatedCajaGui;
                    updateMenuState();
                    updateCajaInfoUI();
                });
            },
            error -> {
                log.error("Error al cerrar CajaDineroEfectivo", error);
                // El error se mostrará automáticamente por LoadingUtil
            }
        );
    }

    /**
     * Procesa el cierre de CajaDineroDigital
     */
    private void procesarCierreDigital(CajaDialogUtil.CloseCajaDineroDigitalData data) {
        log.info("Procesando cierre de CajaDineroDigital");

        loadingUtil.subscribeWithLoading(
            cajaGuiService.closeCajaDineroDigital(
                data.cajaGuiId(), data.cajaDineroDigitalId(), data.cierreDigitalDeclarado()
            ),
            loadingIndicator,
            updatedCajaGui -> {
                log.info("CajaDineroDigital cerrada exitosamente");
                Platform.runLater(() -> {
                    this.currentCajaGui = updatedCajaGui;
                    updateMenuState();
                    updateCajaInfoUI();
                });
            },
            error -> {
                log.error("Error al cerrar CajaDineroDigital", error);
                // El error se mostrará automáticamente por LoadingUtil
            }
        );
    }

    // Métodos para actualizar la UI

    /**
     * Actualiza la información de la caja en la UI
     */
    private void updateCajaInfoUI() {
        if (currentCajaGui == null) {
            lblNombreCaja.setText("No seleccionada");
            lblEstadoEfectivo.setText("No inicializada");
            lblEstadoDigital.setText("No inicializada");
            return;
        }

        lblNombreCaja.setText(currentCajaGui.getNombreCaja());

        // Estado de caja efectivo
        if (currentCajaGui.getCajaDineroEfectivo() == null) {
            lblEstadoEfectivo.setText("No inicializada");
            lblEstadoEfectivo.getStyleClass().removeAll("cobro-amount-label", "cobro-remaining-label");
            lblEstadoEfectivo.getStyleClass().add("cobro-date-label");
        } else if (currentCajaGui.getCajaDineroEfectivo().getCerradaEl() == null) {
            lblEstadoEfectivo.setText("Abierta");
            lblEstadoEfectivo.getStyleClass().removeAll("cobro-date-label", "cobro-remaining-label");
            lblEstadoEfectivo.getStyleClass().add("cobro-amount-label");
        } else {
            lblEstadoEfectivo.setText("Cerrada");
            lblEstadoEfectivo.getStyleClass().removeAll("cobro-date-label", "cobro-amount-label");
            lblEstadoEfectivo.getStyleClass().add("cobro-remaining-label");
        }

        // Estado de caja digital
        if (currentCajaGui.getCajaDineroDigital() == null) {
            lblEstadoDigital.setText("No inicializada");
            lblEstadoDigital.getStyleClass().removeAll("cobro-amount-label", "cobro-remaining-label");
            lblEstadoDigital.getStyleClass().add("cobro-date-label");
        } else if (currentCajaGui.getCajaDineroDigital().getCerradaEl() == null) {
            lblEstadoDigital.setText("Abierta");
            lblEstadoDigital.getStyleClass().removeAll("cobro-date-label", "cobro-remaining-label");
            lblEstadoDigital.getStyleClass().add("cobro-amount-label");
        } else {
            lblEstadoDigital.setText("Cerrada");
            lblEstadoDigital.getStyleClass().removeAll("cobro-date-label", "cobro-amount-label");
            lblEstadoDigital.getStyleClass().add("cobro-remaining-label");
        }
    }

    /**
     * Actualiza la lista de ventas por cobrar en la UI
     */
    private void updateSalesUI() {
        salesContainer.getChildren().clear();

        if (salesPorCobrar.isEmpty()) {
            Label noSalesLabel = new Label("No hay ventas pendientes de cobro");
            noSalesLabel.getStyleClass().add("cobro-date-label");
            salesContainer.getChildren().add(noSalesLabel);
            return;
        }

        for (Sale sale : salesPorCobrar) {
            VBox saleBox = createSaleDisplayBox(sale);
            salesContainer.getChildren().add(saleBox);
        }
    }

    /**
     * Crea un VBox para mostrar información de una venta
     */
    private VBox createSaleDisplayBox(Sale sale) {
        VBox saleBox = new VBox(5);
        saleBox.getStyleClass().add("cobro-detail-container");

        // Información básica de la venta
        Label saleIdLabel = new Label("Venta ID: " + sale.getId().toString().substring(0, 8) + "...");
        saleIdLabel.getStyleClass().add("cobro-user-label");

        Label clienteLabel = new Label("Cliente: " +
            (sale.getCliente() != null ? sale.getCliente().getNombreCompleto() : "Sin cliente"));
        clienteLabel.getStyleClass().add("cobro-info-label");

        Label montoLabel = new Label("Monto Restante: S/ " +
            String.format("%.2f", sale.getTotalRestante()));
        montoLabel.getStyleClass().add("cobro-remaining-label");

        Label tipoLabel = new Label("Tipo: " + sale.getTipoVenta().toString());
        tipoLabel.getStyleClass().add("cobro-date-label");

        saleBox.getChildren().addAll(saleIdLabel, clienteLabel, montoLabel, tipoLabel);

        return saleBox;
    }

    /**
     * Limpia las suscripciones al destruir el controlador
     */
    @Override
    public void cleanup() {
        log.info("Limpiando suscripciones de CajaController");

        if (salesSubscription != null && !salesSubscription.isDisposed()) {
            salesSubscription.dispose();
        }

        if (cajaGuiSubscription != null && !cajaGuiSubscription.isDisposed()) {
            cajaGuiSubscription.dispose();
        }

        super.cleanup();
    }

    // Getters para acceso desde la UI (si es necesario)

    public CajaGui getCurrentCajaGui() {
        return currentCajaGui;
    }

    public ObservableList<Sale> getSalesPorCobrar() {
        return salesPorCobrar;
    }
}
