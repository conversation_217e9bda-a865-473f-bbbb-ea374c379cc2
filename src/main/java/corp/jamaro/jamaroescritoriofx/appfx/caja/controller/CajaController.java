package corp.jamaro.jamaroescritoriofx.appfx.caja.controller;

import corp.jamaro.jamaroescritoriofx.appfx.caja.model.gui.CajaGui;
import corp.jamaro.jamaroescritoriofx.appfx.caja.service.CajaGuiService;
import corp.jamaro.jamaroescritoriofx.appfx.caja.util.CajaDialogUtil;
import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.control.MenuBar;
import javafx.scene.control.MenuItem;
import javafx.scene.control.ProgressIndicator;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;


import java.net.URL;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

/**
 * Controlador principal para la gestión de caja.
 * Maneja la selección de CajaGui y la visualización de cobros pendientes.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class CajaController extends BaseController {

    private final CajaGuiService cajaGuiService;
    private final CajaDialogUtil cajaDialogUtil;

    @FXML
    private StackPane mainStackPane;

    @FXML
    private ProgressIndicator loadingIndicator;

    @FXML
    private MenuBar menuBar;

    @FXML
    private MenuItem menuInicializarEfectivo;

    @FXML
    private MenuItem menuInicializarDigital;

    @FXML
    private MenuItem menuCerrarEfectivo;

    @FXML
    private MenuItem menuCerrarDigital;

    @FXML
    private VBox contentContainer;

    @FXML
    private Label lblNombreCaja;

    @FXML
    private Label lblEstadoEfectivo;

    @FXML
    private Label lblEstadoDigital;

    @FXML
    private VBox salesContainer;

    // Estado del controlador
    private CajaGui currentCajaGui;
    private final ObservableList<Sale> salesPorCobrar = FXCollections.observableArrayList();

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        log.info("Inicializando CajaController");

        // Configurar estado inicial de los menús
        updateMenuState();

        // Configurar eventos del menú
        setupMenuEvents();

        // Inicializar la selección de CajaGui
        initializeCajaGuiSelection();
    }

    /**
     * Configura los eventos del menú bar
     */
    private void setupMenuEvents() {
        menuInicializarEfectivo.setOnAction(e -> handleInicializarEfectivo());
        menuInicializarDigital.setOnAction(e -> handleInicializarDigital());
        menuCerrarEfectivo.setOnAction(e -> handleCerrarEfectivo());
        menuCerrarDigital.setOnAction(e -> handleCerrarDigital());
    }

    /**
     * Actualiza el estado de los elementos del menú según la CajaGui actual
     */
    private void updateMenuState() {
        boolean hasCajaGui = currentCajaGui != null;
        boolean hasEfectivo = hasCajaGui && currentCajaGui.getCajaDineroEfectivo() != null;
        boolean hasDigital = hasCajaGui && currentCajaGui.getCajaDineroDigital() != null;

        menuInicializarEfectivo.setDisable(!hasCajaGui || hasEfectivo);
        menuInicializarDigital.setDisable(!hasCajaGui || hasDigital);
        menuCerrarEfectivo.setDisable(!hasEfectivo);
        menuCerrarDigital.setDisable(!hasDigital);
    }

    /**
     * Inicializa la selección de CajaGui mostrando el diálogo correspondiente
     */
    private void initializeCajaGuiSelection() {
        log.info("Iniciando selección de CajaGui");

        subscribeMonoWithUiUpdate(
            cajaGuiService.getAllCajaGuiOrderedByCreatedAt().collectList(),
            this::handleCajaGuiListReceived,
            error -> {
                log.error("Error al obtener lista de CajaGui", error);
                // Mostrar error y permitir crear nueva caja
                showCreateCajaDialog();
            }
        );
    }

    /**
     * Maneja la lista de CajaGui recibida del servidor
     */
    private void handleCajaGuiListReceived(List<CajaGui> cajaGuiList) {
        if (cajaGuiList.isEmpty()) {
            log.info("No hay CajaGui disponibles, mostrando diálogo de creación");
            showCreateCajaDialog();
        } else {
            log.info("Mostrando diálogo de selección con {} CajaGui disponibles", cajaGuiList.size());
            showCajaSelectionDialog(cajaGuiList);
        }
    }

    /**
     * Muestra el diálogo de selección de CajaGui
     */
    private void showCajaSelectionDialog(List<CajaGui> cajaGuiList) {
        Optional<CajaDialogUtil.CajaSelectionResult> result = cajaDialogUtil.showCajaSelectionDialog(cajaGuiList);

        result.ifPresentOrElse(
            selectionResult -> {
                if (selectionResult.isCreateNew()) {
                    showCreateCajaDialog();
                } else {
                    CajaGui selectedCaja = selectionResult.selectedCajaGui();
                    log.info("CajaGui seleccionada: {} - {}", selectedCaja.getId(), selectedCaja.getNombreCaja());
                    initializeCajaGui(selectedCaja);
                }
            },
            () -> {
                log.info("Selección de CajaGui cancelada");
                // Podríamos cerrar la ventana o mostrar un mensaje
            }
        );
    }

    /**
     * Muestra el diálogo de creación de nueva CajaGui
     */
    private void showCreateCajaDialog() {
        Optional<CajaDialogUtil.CreateCajaData> result = cajaDialogUtil.showCreateCajaDialog();

        result.ifPresentOrElse(
            createData -> {
                log.info("Creando nueva CajaGui: {}", createData.nombreCaja());
                createNewCajaGui(createData.nombreCaja(), createData.guiConfig());
            },
            () -> {
                log.info("Creación de CajaGui cancelada");
                // Volver a mostrar selección si hay cajas disponibles
                initializeCajaGuiSelection();
            }
        );
    }

    /**
     * Crea una nueva CajaGui
     */
    private void createNewCajaGui(String nombreCaja, String guiConfig) {
        subscribeMonoWithUiUpdate(
            cajaGuiService.createCajaGui(nombreCaja, guiConfig),
            this::initializeCajaGui,
            error -> {
                log.error("Error al crear CajaGui", error);
                // Mostrar error y volver al diálogo de creación
                showCreateCajaDialog();
            }
        );
    }

    /**
     * Inicializa la CajaGui seleccionada y se suscribe a sus cambios
     */
    private void initializeCajaGui(CajaGui cajaGui) {
        log.info("Inicializando CajaGui: {} - {}", cajaGui.getId(), cajaGui.getNombreCaja());

        this.currentCajaGui = cajaGui;

        updateMenuState();
        updateCajaInfoUI();

        // Suscribirse a cambios de la CajaGui
        subscribeToCurrentCajaGui();

        // Suscribirse a ventas por cobrar
        subscribeToSalesPorCobrar();
    }

    /**
     * Se suscribe a los cambios de la CajaGui actual
     */
    private void subscribeToCurrentCajaGui() {
        if (currentCajaGui == null) {
            log.warn("No se puede suscribir a CajaGui: currentCajaGui es null");
            return;
        }

        // Usar subscripción sin timeout para CajaGui ya que puede no emitir actualizaciones frecuentes
        subscribeFluxWithUiUpdateNoTimeout(
            cajaGuiService.subscribeToCurrentCajaGui(currentCajaGui.getId()),
            updatedCajaGui -> {
                log.debug("Recibida actualización de CajaGui: {}", updatedCajaGui.getNombreCaja());
                this.currentCajaGui = updatedCajaGui;
                updateMenuState();
                updateCajaInfoUI();
            },
            error -> log.error("Error en suscripción a CajaGui", error)
        );
    }

    /**
     * Se suscribe a las ventas por cobrar
     */
    private void subscribeToSalesPorCobrar() {
        log.info("[DEBUG_LOG] Iniciando suscripción a ventas por cobrar");
        // Usar subscripción sin timeout para Sales por cobrar ya que puede no emitir actualizaciones frecuentes
        subscribeFluxWithUiUpdateNoTimeout(
            cajaGuiService.subscribeToSalesPorCobrar(),
            salesList -> {
                log.info("[DEBUG_LOG] CajaController recibió {} ventas por cobrar", salesList.size());
                if (salesList != null && !salesList.isEmpty()) {
                    for (int i = 0; i < salesList.size(); i++) {
                        Sale sale = salesList.get(i);
                        log.debug("[DEBUG_LOG] Sale {}: ID={}, totalRestante={}, tipoVenta={}, cliente={}", 
                            i + 1, 
                            sale.getId(), 
                            sale.getTotalRestante(), 
                            sale.getTipoVenta(),
                            sale.getCliente() != null ? sale.getCliente().getId() : "null");
                    }
                }
                
                log.debug("[DEBUG_LOG] Actualizando ObservableList con {} elementos", salesList.size());
                salesPorCobrar.setAll(salesList);
                log.debug("[DEBUG_LOG] ObservableList actualizada, tamaño actual: {}", salesPorCobrar.size());
                
                log.debug("[DEBUG_LOG] Llamando updateSalesUI()");
                updateSalesUI();
                log.debug("[DEBUG_LOG] updateSalesUI() completado");
            },
            error -> {
                log.error("[DEBUG_LOG] Error en suscripción a ventas por cobrar: {}", error.getMessage(), error);
                log.error("Error en suscripción a ventas por cobrar", error);
            }
        );
    }

    // Métodos para manejar eventos del menú

    /**
     * Maneja la inicialización de CajaDineroEfectivo
     */
    private void handleInicializarEfectivo() {
        if (currentCajaGui == null) {
            log.warn("No se puede inicializar efectivo: no hay CajaGui seleccionada");
            return;
        }

        log.info("Iniciando proceso de inicialización de CajaDineroEfectivo");
        cajaDialogUtil.showInitializeCajaDineroEfectivoDialog(currentCajaGui.getId())
            .ifPresent(this::procesarInicializacionEfectivo);
    }

    /**
     * Maneja la inicialización de CajaDineroDigital
     */
    private void handleInicializarDigital() {
        if (currentCajaGui == null) {
            log.warn("No se puede inicializar digital: no hay CajaGui seleccionada");
            return;
        }

        log.info("Iniciando proceso de inicialización de CajaDineroDigital");
        cajaDialogUtil.showInitializeCajaDineroDigitalDialog(currentCajaGui.getId())
            .ifPresent(this::procesarInicializacionDigital);
    }

    /**
     * Maneja el cierre de CajaDineroEfectivo
     */
    private void handleCerrarEfectivo() {
        if (currentCajaGui == null || currentCajaGui.getCajaDineroEfectivo() == null) {
            log.warn("No se puede cerrar efectivo: no hay CajaDineroEfectivo disponible");
            return;
        }

        log.info("Iniciando proceso de cierre de CajaDineroEfectivo");
        cajaDialogUtil.showCloseCajaDineroEfectivoDialog(
            currentCajaGui.getId(),
            currentCajaGui.getCajaDineroEfectivo().getId()
        ).ifPresent(this::procesarCierreEfectivo);
    }

    /**
     * Maneja el cierre de CajaDineroDigital
     */
    private void handleCerrarDigital() {
        if (currentCajaGui == null || currentCajaGui.getCajaDineroDigital() == null) {
            log.warn("No se puede cerrar digital: no hay CajaDineroDigital disponible");
            return;
        }

        log.info("Iniciando proceso de cierre de CajaDineroDigital");
        cajaDialogUtil.showCloseCajaDineroDigitalDialog(
            currentCajaGui.getId(),
            currentCajaGui.getCajaDineroDigital().getId()
        ).ifPresent(this::procesarCierreDigital);
    }

    // Métodos para procesar las operaciones con el servidor

    /**
     * Procesa la inicialización de CajaDineroEfectivo
     */
    private void procesarInicializacionEfectivo(CajaDialogUtil.InitializeCajaDineroEfectivoData data) {
        log.info("Procesando inicialización de CajaDineroEfectivo");

        subscribeMonoWithUiUpdate(
            cajaGuiService.initializeCajaDineroEfectivo(
                data.cajaGuiId(), data.nombre(), data.montoInicialEfectivo(),
                data.diezCentimos(), data.veinteCentimos(), data.cincuentaCentimos(),
                data.unSol(), data.dosSoles(), data.cincoSoles(),
                data.diezSoles(), data.veinteSoles(), data.cincuentaSoles(),
                data.cienSoles(), data.doscientosSoles()
            ),
            updatedCajaGui -> {
                log.info("CajaDineroEfectivo inicializada exitosamente");
                this.currentCajaGui = updatedCajaGui;
                updateMenuState();
                updateCajaInfoUI();
            },
            error -> log.error("Error al inicializar CajaDineroEfectivo", error)
        );
    }

    /**
     * Procesa la inicialización de CajaDineroDigital
     */
    private void procesarInicializacionDigital(CajaDialogUtil.InitializeCajaDineroDigitalData data) {
        log.info("Procesando inicialización de CajaDineroDigital");

        subscribeMonoWithUiUpdate(
            cajaGuiService.initializeCajaDineroDigital(
                data.cajaGuiId(), data.cuentaDigitalAsignada(), data.montoInicialDigital()
            ),
            updatedCajaGui -> {
                log.info("CajaDineroDigital inicializada exitosamente");
                this.currentCajaGui = updatedCajaGui;
                updateMenuState();
                updateCajaInfoUI();
            },
            error -> log.error("Error al inicializar CajaDineroDigital", error)
        );
    }

    /**
     * Procesa el cierre de CajaDineroEfectivo
     */
    private void procesarCierreEfectivo(CajaDialogUtil.CloseCajaDineroEfectivoData data) {
        log.info("Procesando cierre de CajaDineroEfectivo");

        subscribeMonoWithUiUpdate(
            cajaGuiService.closeCajaDineroEfectivo(
                data.cajaGuiId(), data.cajaDineroEfectivoId(),
                data.cierreDiezCentimos(), data.cierreVeinteCentimos(), data.cierreCincuentaCentimos(),
                data.cierreUnSol(), data.cierreDosSoles(), data.cierreCincoSoles(),
                data.cierreDiezSoles(), data.cierreVeinteSoles(), data.cierreCincuentaSoles(),
                data.cierreCienSoles(), data.cierreDoscientosSoles()
            ),
            updatedCajaGui -> {
                log.info("CajaDineroEfectivo cerrada exitosamente");
                this.currentCajaGui = updatedCajaGui;
                updateMenuState();
                updateCajaInfoUI();
            },
            error -> log.error("Error al cerrar CajaDineroEfectivo", error)
        );
    }

    /**
     * Procesa el cierre de CajaDineroDigital
     */
    private void procesarCierreDigital(CajaDialogUtil.CloseCajaDineroDigitalData data) {
        log.info("Procesando cierre de CajaDineroDigital");

        subscribeMonoWithUiUpdate(
            cajaGuiService.closeCajaDineroDigital(
                data.cajaGuiId(), data.cajaDineroDigitalId(), data.cierreDigitalDeclarado()
            ),
            updatedCajaGui -> {
                log.info("CajaDineroDigital cerrada exitosamente");
                this.currentCajaGui = updatedCajaGui;
                updateMenuState();
                updateCajaInfoUI();
            },
            error -> log.error("Error al cerrar CajaDineroDigital", error)
        );
    }

    // Métodos para actualizar la UI

    /**
     * Actualiza la información de la caja en la UI
     */
    private void updateCajaInfoUI() {
        if (currentCajaGui == null) {
            lblNombreCaja.setText("No seleccionada");
            lblEstadoEfectivo.setText("No inicializada");
            lblEstadoDigital.setText("No inicializada");
            return;
        }

        lblNombreCaja.setText(currentCajaGui.getNombreCaja());

        // Estado de caja efectivo
        if (currentCajaGui.getCajaDineroEfectivo() == null) {
            lblEstadoEfectivo.setText("No inicializada");
            lblEstadoEfectivo.getStyleClass().removeAll("cobro-amount-label", "cobro-remaining-label");
            lblEstadoEfectivo.getStyleClass().add("cobro-date-label");
        } else if (currentCajaGui.getCajaDineroEfectivo().getCerradaEl() == null) {
            lblEstadoEfectivo.setText("Abierta");
            lblEstadoEfectivo.getStyleClass().removeAll("cobro-date-label", "cobro-remaining-label");
            lblEstadoEfectivo.getStyleClass().add("cobro-amount-label");
        } else {
            lblEstadoEfectivo.setText("Cerrada");
            lblEstadoEfectivo.getStyleClass().removeAll("cobro-date-label", "cobro-amount-label");
            lblEstadoEfectivo.getStyleClass().add("cobro-remaining-label");
        }

        // Estado de caja digital
        if (currentCajaGui.getCajaDineroDigital() == null) {
            lblEstadoDigital.setText("No inicializada");
            lblEstadoDigital.getStyleClass().removeAll("cobro-amount-label", "cobro-remaining-label");
            lblEstadoDigital.getStyleClass().add("cobro-date-label");
        } else if (currentCajaGui.getCajaDineroDigital().getCerradaEl() == null) {
            lblEstadoDigital.setText("Abierta");
            lblEstadoDigital.getStyleClass().removeAll("cobro-date-label", "cobro-remaining-label");
            lblEstadoDigital.getStyleClass().add("cobro-amount-label");
        } else {
            lblEstadoDigital.setText("Cerrada");
            lblEstadoDigital.getStyleClass().removeAll("cobro-date-label", "cobro-amount-label");
            lblEstadoDigital.getStyleClass().add("cobro-remaining-label");
        }
    }

    /**
     * Actualiza la lista de ventas por cobrar en la UI
     */
    private void updateSalesUI() {
        log.debug("[DEBUG_LOG] Iniciando updateSalesUI(), salesPorCobrar.size()={}", salesPorCobrar.size());
        log.debug("[DEBUG_LOG] salesContainer actual tiene {} hijos", salesContainer.getChildren().size());
        
        salesContainer.getChildren().clear();
        log.debug("[DEBUG_LOG] salesContainer limpiado");

        if (salesPorCobrar.isEmpty()) {
            log.info("[DEBUG_LOG] No hay ventas por cobrar, mostrando mensaje vacío");
            Label noSalesLabel = new Label("No hay ventas pendientes de cobro");
            noSalesLabel.getStyleClass().add("cobro-date-label");
            salesContainer.getChildren().add(noSalesLabel);
            log.debug("[DEBUG_LOG] Mensaje 'no hay ventas' agregado al container");
            return;
        }

        log.info("[DEBUG_LOG] Creando {} elementos de UI para las ventas", salesPorCobrar.size());
        for (int i = 0; i < salesPorCobrar.size(); i++) {
            Sale sale = salesPorCobrar.get(i);
            log.debug("[DEBUG_LOG] Creando UI para Sale {}: ID={}", i + 1, sale.getId());
            VBox saleBox = createSaleDisplayBox(sale);
            salesContainer.getChildren().add(saleBox);
            log.debug("[DEBUG_LOG] Sale {} agregado al container", i + 1);
        }
        
        log.info("[DEBUG_LOG] updateSalesUI() completado, salesContainer ahora tiene {} hijos", 
            salesContainer.getChildren().size());
    }

    /**
     * Crea un VBox para mostrar información de una venta
     */
    private VBox createSaleDisplayBox(Sale sale) {
        VBox saleBox = new VBox(5);
        saleBox.getStyleClass().add("cobro-detail-container");

        // Información básica de la venta
        Label saleIdLabel = new Label("Venta ID: " + sale.getId().toString().substring(0, 8) + "...");
        saleIdLabel.getStyleClass().add("cobro-user-label");

        Label clienteLabel = new Label("Cliente: " +
            (sale.getCliente() != null ? formatClienteDisplay(sale.getCliente()) : "Sin cliente"));
        clienteLabel.getStyleClass().add("cobro-info-label");

        Label montoLabel = new Label("Monto Restante: S/ " +
            String.format("%.2f", sale.getTotalRestante()));
        montoLabel.getStyleClass().add("cobro-remaining-label");

        Label tipoLabel = new Label("Tipo: " + sale.getTipoVenta().toString());
        tipoLabel.getStyleClass().add("cobro-date-label");

        saleBox.getChildren().addAll(saleIdLabel, clienteLabel, montoLabel, tipoLabel);

        return saleBox;
    }

    /**
     * Formatea la información del cliente para mostrar
     */
    private String formatClienteDisplay(corp.jamaro.jamaroescritoriofx.appfx.model.Cliente cliente) {
        if (cliente == null) return "Sin cliente";

        StringBuilder display = new StringBuilder();

        // Agregar nombre o razón social
        String nombre = "";
        if (cliente.getRazonSocial() != null && !cliente.getRazonSocial().isEmpty()) {
            nombre = cliente.getRazonSocial();
        } else if (cliente.getNombre() != null && !cliente.getNombre().isEmpty()) {
            nombre = cliente.getNombre();
            if (cliente.getApellido() != null && !cliente.getApellido().isEmpty()) {
                nombre += " " + cliente.getApellido();
            }
        }

        if (!nombre.isEmpty()) {
            display.append(nombre);
        }

        // Agregar documento si hay nombre
        if (!display.isEmpty()) {
            if (cliente.getDni() != null && !cliente.getDni().isEmpty()) {
                display.append(" (").append(cliente.getDni()).append(")");
            } else if (cliente.getRuc() != null && !cliente.getRuc().isEmpty()) {
                display.append(" (").append(cliente.getRuc()).append(")");
            } else if (cliente.getOtroDocumento() != null && !cliente.getOtroDocumento().isEmpty()) {
                display.append(" (").append(cliente.getOtroDocumento()).append(")");
            }
        } else {
            // Si no hay nombre, mostrar solo el documento
            if (cliente.getDni() != null && !cliente.getDni().isEmpty()) {
                display.append(cliente.getDni());
            } else if (cliente.getRuc() != null && !cliente.getRuc().isEmpty()) {
                display.append(cliente.getRuc());
            } else if (cliente.getOtroDocumento() != null && !cliente.getOtroDocumento().isEmpty()) {
                display.append(cliente.getOtroDocumento());
            } else {
                display.append("Cliente sin datos");
            }
        }

        return display.toString();
    }

    /**
     * Limpia las suscripciones al destruir el controlador
     */
    public void cleanup() {
        log.info("Limpiando suscripciones de CajaController");
        onClose(); // Usa el método del BaseController para limpiar suscripciones
    }

    // Getters para acceso desde la UI (si es necesario)

    public CajaGui getCurrentCajaGui() {
        return currentCajaGui;
    }

    public ObservableList<Sale> getSalesPorCobrar() {
        return salesPorCobrar;
    }
}
