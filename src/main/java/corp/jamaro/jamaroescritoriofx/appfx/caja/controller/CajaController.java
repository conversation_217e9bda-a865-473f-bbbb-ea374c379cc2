package corp.jamaro.jamaroescritoriofx.appfx.caja.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * Controlador principal para la gestión de caja.
 * Maneja la selección de CajaGui y la visualización de cobros pendientes.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class CajaController extends BaseController {
    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {

    }
}
