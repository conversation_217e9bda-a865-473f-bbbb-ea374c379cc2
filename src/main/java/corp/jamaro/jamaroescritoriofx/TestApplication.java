//package corp.jamaro.jamaroescritoriofx;
//
//import corp.jamaro.jamaroescritoriofx.security.ConnectionService;
//import corp.jamaro.jamaroescritoriofx.venta.service.gui.VentasPrincipalGuiService;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.boot.SpringApplication;
//import org.springframework.boot.autoconfigure.SpringBootApplication;
//import org.springframework.context.annotation.Bean;
//import reactor.core.publisher.Mono;
//
//@SpringBootApplication
//public class TestApplication {
//
//    public static void main(String[] args) {
//        SpringApplication.run(TestApplication.class, args);
//    }
//
//    @Bean
//    CommandLineRunner run(ConnectionService connectionService, VentasPrincipalGuiService ventasPrincipalGuiService) {
//        return args -> {
//            String rfid = "jose123";
//
//            // Autenticación con RFID
//            connectionService.loginByRfid(rfid)
//                    .doOnNext(response -> System.out.println("Autenticación exitosa: " + response))
//                    .doOnError(error -> System.err.println("Error durante la autenticación: " + error.getMessage()))
//                    .flatMapMany(authenticated -> {
//                        // Suscripción a las actualizaciones después de autenticarse
//                        return ventasPrincipalGuiService.updates()
//                                .doOnNext(update -> System.out.println("Actualización recibida: " + update))
//                                .doOnError(error -> System.err.println("Error en la suscripción: " + error.getMessage()));
//                    })
//                    .subscribe();
//
//            // Mantener la aplicación corriendo
//            Thread.currentThread().join();
//        };
//    }
//}
