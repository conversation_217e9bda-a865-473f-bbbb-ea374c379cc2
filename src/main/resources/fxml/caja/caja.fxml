<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Menu?>
<?import javafx.scene.control.MenuBar?>
<?import javafx.scene.control.MenuItem?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Priority?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>

<StackPane fx:id="mainStackPane" prefHeight="720.0" prefWidth="1200.0" stylesheets="@../../css/caja.css" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.caja.controller.CajaController">
   <children>
      <!-- Contenido principal -->
      <VBox spacing="0.0">
         <children>
            <!-- Menu Bar -->
            <MenuBar fx:id="menuBar">
               <menus>
                  <Menu text="Caja Efectivo">
                     <items>
                        <MenuItem fx:id="menuInicializarEfectivo" text="Inicializar Caja Efectivo" />
                        <MenuItem fx:id="menuCerrarEfectivo" text="Cerrar Caja Efectivo" />
                     </items>
                  </Menu>
                  <Menu text="Caja Digital">
                     <items>
                        <MenuItem fx:id="menuInicializarDigital" text="Inicializar Caja Digital" />
                        <MenuItem fx:id="menuCerrarDigital" text="Cerrar Caja Digital" />
                     </items>
                  </Menu>
               </menus>
            </MenuBar>

            <!-- Contenedor principal -->
            <VBox fx:id="contentContainer" spacing="10.0" VBox.vgrow="ALWAYS">
               <padding>
                  <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
               </padding>
               <children>
                  <!-- Información de la caja actual -->
                  <VBox spacing="5.0" styleClass="cobro-detail-container">
                     <children>
                        <Label text="Información de Caja" styleClass="cobro-info-label" />
                        <Separator />
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <children>
                              <Label text="Caja:" styleClass="cobro-user-label" />
                              <Label fx:id="lblNombreCaja" text="No seleccionada" styleClass="cobro-amount-label" />
                           </children>
                        </HBox>
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <children>
                              <Label text="Estado Efectivo:" styleClass="cobro-user-label" />
                              <Label fx:id="lblEstadoEfectivo" text="No inicializada" styleClass="cobro-date-label" />
                           </children>
                        </HBox>
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <children>
                              <Label text="Estado Digital:" styleClass="cobro-user-label" />
                              <Label fx:id="lblEstadoDigital" text="No inicializada" styleClass="cobro-date-label" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>

                  <!-- Área de ventas por cobrar -->
                  <VBox spacing="5.0" VBox.vgrow="ALWAYS">
                     <children>
                        <Label text="Ventas por Cobrar" styleClass="cobro-info-label" />
                        <Separator />
                        <ScrollPane fitToWidth="true" VBox.vgrow="ALWAYS">
                           <content>
                              <VBox fx:id="salesContainer" spacing="5.0">
                                 <padding>
                                    <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                                 </padding>
                                 <!-- Las ventas se agregarán dinámicamente aquí -->
                              </VBox>
                           </content>
                        </ScrollPane>
                     </children>
                  </VBox>
               </children>
            </VBox>
         </children>
      </VBox>

      <!-- Loading indicator -->
      <ProgressIndicator fx:id="loadingIndicator" visible="false" />
   </children>
</StackPane>
