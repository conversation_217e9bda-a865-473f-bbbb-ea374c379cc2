<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.layout.StackPane?>

<StackPane fx:id="mainStackPane" prefHeight="720.0" prefWidth="1200.0" stylesheets="@../../css/caja.css" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.caja.controller.CajaController">
   <children>
      <!-- Loading indicator -->
      <ProgressIndicator fx:id="loadingIndicator" visible="false" />
   </children>
</StackPane>
